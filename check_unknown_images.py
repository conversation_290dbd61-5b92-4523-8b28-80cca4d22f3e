#!/usr/bin/env python3
"""Check for products with 'unknown' in their image URLs"""

import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv

load_dotenv()

def check_unknown_images():
    """Check for products with 'unknown' in their image URLs"""
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL not found")
        return
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # Check for products with 'unknown' in image URL
        print("=== Products with 'unknown' in image URL ===")
        cur.execute("""
            SELECT id, name, image_url 
            FROM uses_products 
            WHERE LOWER(image_url) LIKE '%unknown%'
            ORDER BY id
        """)
        
        unknown_products = cur.fetchall()
        
        if unknown_products:
            print(f"\nFound {len(unknown_products)} products with 'unknown' in image URL:")
            for id, name, url in unknown_products:
                print(f"\nID: {id}")
                print(f"Name: {name}")
                print(f"URL: {url}")
        else:
            print("\nNo products found with 'unknown' in image URL")
        
        # Check for any Supabase storage URLs containing 'unknown'
        print("\n\n=== All Supabase storage URLs containing 'unknown' ===")
        cur.execute("""
            SELECT id, name, image_url 
            FROM uses_products 
            WHERE image_url LIKE '%supabase%' 
            AND LOWER(image_url) LIKE '%unknown%'
            ORDER BY id
        """)
        
        supabase_unknown = cur.fetchall()
        
        if supabase_unknown:
            print(f"\nFound {len(supabase_unknown)} Supabase URLs with 'unknown':")
            for id, name, url in supabase_unknown:
                print(f"\nID: {id}")
                print(f"Name: {name}")
                print(f"URL: {url}")
        else:
            print("\nNo Supabase URLs found with 'unknown'")
        
        # Look for specific "Unknown Hemp Image" filename
        print("\n\n=== Checking for 'Unknown Hemp Image' file ===")
        cur.execute("""
            SELECT id, name, image_url 
            FROM uses_products 
            WHERE LOWER(image_url) LIKE '%unknown%hemp%image%'
            OR LOWER(image_url) LIKE '%unknown_hemp_image%'
            ORDER BY id
        """)
        
        specific_unknown = cur.fetchall()
        
        if specific_unknown:
            print(f"\nFound {len(specific_unknown)} products with 'Unknown Hemp Image':")
            for id, name, url in specific_unknown:
                print(f"\nID: {id}")
                print(f"Name: {name}")
                print(f"URL: {url}")
        else:
            print("\nNo products found with 'Unknown Hemp Image' filename")
        
        # Check total products without images
        print("\n\n=== Products without images ===")
        cur.execute("""
            SELECT COUNT(*) 
            FROM uses_products 
            WHERE image_url IS NULL OR image_url = ''
        """)
        
        no_image_count = cur.fetchone()[0]
        print(f"Products with NULL or empty image_url: {no_image_count}")
        
        # Get sample of products without images
        if no_image_count > 0:
            cur.execute("""
                SELECT id, name 
                FROM uses_products 
                WHERE image_url IS NULL OR image_url = ''
                LIMIT 5
            """)
            samples = cur.fetchall()
            print("\nSample products without images:")
            for id, name in samples:
                print(f"  ID {id}: {name}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_unknown_images()