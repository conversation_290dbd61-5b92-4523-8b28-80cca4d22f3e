#!/usr/bin/env python3
import os
import subprocess
import sys

# Read .env file
if os.path.exists('.env'):
    with open('.env', 'r') as f:
        for line in f:
            if '=' in line and not line.strip().startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value.strip('"').strip("'")

# Get DATABASE_URL
database_url = os.environ.get('DATABASE_URL')
if not database_url:
    print("ERROR: DATABASE_URL not found")
    sys.exit(1)

# Use psql directly
query = """
SELECT 
    CASE 
        WHEN image_url LIKE '%unknown-hemp-product.webp%' THEN 'Supabase Fallback'
        WHEN image_url LIKE '%supabase.co%' THEN 'Other Supabase'
        WHEN image_url LIKE '%replicate.delivery%' THEN 'Replicate AI'
        WHEN image_url IS NULL OR image_url = '' THEN 'No Image'
        ELSE 'Other'
    END as category,
    COUNT(*) as count
FROM uses_products
GROUP BY category
ORDER BY count DESC;
"""

# Run query
result = subprocess.run(
    ['psql', database_url, '-t', '-c', query],
    capture_output=True,
    text=True
)

if result.returncode == 0:
    print("IMAGE URL DISTRIBUTION:")
    print("-" * 50)
    print(result.stdout)
    
    # Get total count
    total_query = "SELECT COUNT(*) FROM uses_products;"
    total_result = subprocess.run(
        ['psql', database_url, '-t', '-c', total_query],
        capture_output=True,
        text=True
    )
    
    if total_result.returncode == 0:
        total = int(total_result.stdout.strip())
        print(f"\nTotal products: {total}")
    
    # Check specific fallback URL
    fallback_query = """
    SELECT COUNT(*) 
    FROM uses_products 
    WHERE image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/unknown-hemp-product.webp';
    """
    
    fallback_result = subprocess.run(
        ['psql', database_url, '-t', '-c', fallback_query],
        capture_output=True,
        text=True
    )
    
    if fallback_result.returncode == 0:
        fallback_count = int(fallback_result.stdout.strip())
        print(f"\nProducts using exact Supabase fallback URL: {fallback_count}")
        
    # Get sample products with fallback
    sample_query = """
    SELECT id, name 
    FROM uses_products 
    WHERE image_url LIKE '%unknown-hemp-product.webp%'
    LIMIT 5;
    """
    
    sample_result = subprocess.run(
        ['psql', database_url, '-t', '-c', sample_query],
        capture_output=True,
        text=True
    )
    
    if sample_result.returncode == 0 and sample_result.stdout.strip():
        print("\nSample products using fallback image:")
        print(sample_result.stdout)
        
else:
    print(f"Error running query: {result.stderr}")