/* Enhanced Color Scheme and Visual Hierarchy */
:root {
  /* Default to dark mode - Black/Purple/Hemp Green Theme */
  /* Primary Colors - Pure Black */
  --primary: 0 0% 0%; /* Pure black primary */
  --primary-foreground: 0 0% 100%;
  
  /* Secondary Colors - Purple for System Actions */
  --secondary: 258 90% 66%; /* #8b5cf6 - Vibrant purple */
  --secondary-foreground: 0 0% 100%;
  
  /* Accent Colors - Hemp Green */
  --accent: 142 70% 45%; /* #22c55e - Hemp green */
  --accent-foreground: 0 0% 0%;
  
  /* Additional shades for UI elements */
  --purple-light: 258 90% 75%;
  --purple-dark: 258 90% 45%;
  --hemp-light: 142 70% 60%;
  --hemp-dark: 142 70% 35%;
  
  /* Background Colors - True Black Theme */
  --background: 0 0% 0%; /* Pure black background */
  --foreground: 0 0% 100%;
  
  /* Card and Muted Elements */
  --card: 0 0% 4%; /* Very dark gray */
  --card-foreground: 0 0% 100%;
  --muted: 0 0% 10%;
  --muted-foreground: 0 0% 60%;
  
  /* Borders and Inputs */
  --border: 0 0% 20%;
  --input: 0 0% 14.9%;
  
  /* Status Colors */
  --destructive: 0 62.8% 50%;
  --destructive-foreground: 0 0% 98%;
  
  /* Ring and Radius */
  --ring: 134 61% 41%;
  --radius: 0.5rem;
  
  /* Dark Theme Colors (replacing marine) */
  --dark-bg: 0 0% 0%;
  --dark-sidebar: 0 0% 2%;
  --dark-card: 0 0% 4%;
  --dark-border: 0 0% 10%;
  --dark-hover: 0 0% 6%;
  --text-primary: 0 0% 100%;
  --text-secondary: 0 0% 70%;
  --text-muted: 0 0% 50%;
}

/* Light mode */
.light {
  /* Primary Colors - Black Interface Theme */
  --primary: 0 0% 0%; /* Pure black primary */
  --primary-foreground: 0 0% 100%;

  /* Secondary Colors - Purple for Interactive Elements */
  --secondary: 258 90% 66%; /* Purple #8b5cf6 */
  --secondary-foreground: 0 0% 100%;

  /* Hemp Accent - Green for Hemp-Specific Elements Only */
  --accent: 142 70% 45%; /* Hemp green #22c55e */
  --accent-foreground: 0 0% 100%;

  /* Additional shades for UI elements */
  --purple-light: 258 90% 75%;
  --purple-dark: 258 90% 45%;
  --hemp-light: 142 70% 60%;
  --hemp-dark: 142 70% 35%;

  /* Background Colors for Light Mode */
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;

  /* Card and Muted Elements */
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;

  /* Borders and Inputs */
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;

  /* Status Colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;

  /* Ring and Radius */
  --ring: 258 90% 66%; /* Purple focus rings */

  /* Interface Colors for Light Mode */
  --interface-bg: 0 0% 98%;
  --interface-card: 0 0% 100%;
  --interface-border: 0 0% 89%;
  --interface-text: 0 0% 9%;
  --interface-text-secondary: 0 0% 45%;
  --interface-text-muted: 0 0% 64%;
}

/* Dark mode adjustments */
.dark {
  --primary: 0 0% 0%; /* Pure black */
  --secondary: 258 90% 66%; /* Purple #8b5cf6 */
  --accent: 142 70% 45%; /* Hemp green #22c55e */
  --background: 221 39% 11%; /* Dark gray #111827 */
  --foreground: 0 0% 95%;
  --card: 0 0% 0%; /* Pure black cards */
  --card-foreground: 0 0% 95%;
  --muted: 220 13% 18%;
  --muted-foreground: 220 9% 46%;
  --border: 217 19% 27%;
  --input: 217 19% 27%;
  --ring: 258 90% 66%; /* Purple focus rings */

  /* Interface Colors for Dark Mode */
  --interface-bg: 0 0% 0%;
  --interface-card: 220 13% 9%;
  --interface-border: 217 19% 27%;
  --interface-text: 0 0% 95%;
  --interface-text-secondary: 220 9% 65%;
  --interface-text-muted: 220 9% 46%;
}

/* Typography Scale */
.text-display {
  @apply text-5xl font-bold tracking-tight lg:text-6xl;
}

.text-headline {
  @apply text-3xl font-bold lg:text-4xl;
}

.text-title {
  @apply text-2xl font-semibold lg:text-3xl;
}

.text-subtitle {
  @apply text-xl font-medium lg:text-2xl;
}

.text-body-large {
  @apply text-lg;
}

.text-body {
  @apply text-base;
}

.text-body-small {
  @apply text-sm;
}

.text-caption {
  @apply text-xs;
}

/* Enhanced Shadows */
.shadow-subtle {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.shadow-soft {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.shadow-medium {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.shadow-strong {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.shadow-glow-primary {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.shadow-glow-secondary {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
  @apply bg-gradient-to-br from-black to-gray-900;
}

.bg-gradient-secondary {
  @apply bg-gradient-to-br from-purple-600 to-purple-800;
}

.bg-gradient-accent {
  @apply bg-gradient-to-br from-hemp-400 to-hemp-600;
}

.bg-gradient-dark {
  @apply bg-gradient-to-b from-gray-900 to-black;
}

.bg-gradient-card {
  @apply bg-gradient-to-br from-gray-900/50 to-gray-800/50;
}

/* Enhanced Hover States */
.hover-lift {
  @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
}

.hover-glow {
  @apply transition-all duration-300;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); /* Purple glow */
}

.hover-brightness {
  @apply transition-all duration-300 hover:brightness-110;
}

/* Focus States */
.focus-ring-primary {
  @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-black;
}

.focus-ring-secondary {
  @apply focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-black;
}

.focus-ring-hemp {
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-black;
}

/* Animation Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Visual Hierarchy Utilities */
.content-primary {
  @apply text-white;
}

.content-secondary {
  @apply text-gray-300;
}

.content-tertiary {
  @apply text-gray-400;
}

.content-disabled {
  @apply text-gray-600;
}

/* Interactive Feedback */
.interactive-bounce {
  @apply transition-transform active:scale-95;
}

.interactive-glow {
  @apply transition-all;
}

.interactive-glow:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); /* Purple glow */
}

.interactive-glow:active {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5); /* Stronger purple glow */
}

.interactive-glow-hemp:hover {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3); /* Hemp green glow for hemp-specific elements */
}