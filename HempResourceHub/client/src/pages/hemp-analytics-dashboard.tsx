import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { HempKPICard } from "@/components/ui/hemp-kpi-card";
import { HempProductCard } from "@/components/ui/hemp-product-card";
import { ComplexityChart } from "@/components/charts/ComplexityChart";
import { ProcessingTimeChart } from "@/components/charts/ProcessingTimeChart";
import { IndustryDistributionChart } from "@/components/charts/IndustryDistributionChart";
import { PlantPartUsageChart } from "@/components/charts/PlantPartUsageChart";
import { GrowthTrendChart } from "@/components/charts/GrowthTrendChart";
import { HeatmapChart } from "@/components/charts/HeatmapChart";
import { InsightSummary } from "@/components/ui/insight-summary";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AnalyticsDataTable } from "@/components/ui/analytics-data-table";
import { MetricsSummary } from "@/components/ui/metrics-summary";
import { But<PERSON> } from "@/components/ui/button";
import { Filter, RefreshCw, Bar<PERSON>hart3 } from "lucide-react";
import { AnalyticsFilterPanel, FilterState } from "@/components/ui/analytics-filter-panel";
import { DataExplorerTab } from "@/components/analytics/DataExplorerTab";

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  processingMethods: [],
  productStatus: "all",
  priceRange: [0, 10000],
  sustainabilityScore: [0, 100],
};

export default function HempAnalyticsDashboard() {
  const [filters, setFilters] = useState<FilterState>(defaultFilters);
  const [filtersOpen, setFiltersOpen] = useState(false);

  // KPI data for hemp industry
  const kpis = [
    {
      category: "Fiber Products",
      productCount: 234,
      growth: "+82.5%",
      revenue: "$2.4M",
      icon: "package" as const,
    },
    {
      category: "CBD Products", 
      productCount: 187,
      growth: "+94.2%",
      revenue: "$4.8M",
      icon: "trending" as const,
    },
    {
      category: "Food & Beverage",
      productCount: 293,
      growth: "+67.8%",
      revenue: "$1.8M",
      icon: "leaf" as const,
    },
  ];

  // Hemp product data for grid
  const products = [
    {
      name: "Premium Hemp Fiber Textile",
      category: "Textiles",
      plantPart: "Fiber",
      price: "$45/kg",
      sustainabilityScore: 92,
      processingMethod: "Decortication",
      company: "HempTex Industries",
    },
    {
      name: "Full Spectrum CBD Oil",
      category: "Medical",
      plantPart: "Flower",
      price: "$120/ml",
      sustainabilityScore: 98,
      processingMethod: "CO2 Extraction",
      company: "Pure Hemp Labs",
    },
    {
      name: "Organic Hemp Protein Powder",
      category: "Food & Beverage",
      plantPart: "Seed",
      price: "$32/kg",
      sustainabilityScore: 85,
      processingMethod: "Cold-Pressed",
      company: "NutriHemp Co",
    },
    {
      name: "Hempcrete Building Blocks",
      category: "Construction",
      plantPart: "Stalk",
      price: "$18/block",
      sustainabilityScore: 94,
      processingMethod: "Mineralization",
      company: "EcoBuild Hemp",
    },
    {
      name: "Cold-Pressed Hemp Seed Oil",
      category: "Cosmetics",
      plantPart: "Seed",
      price: "$85/L",
      sustainabilityScore: 96,
      processingMethod: "Cold-Pressed",
      company: "Hemp Beauty Co",
    },
    {
      name: "Hemp Bioplastic Pellets",
      category: "Automotive",
      plantPart: "Fiber",
      price: "$125/kg",
      sustainabilityScore: 78,
      processingMethod: "Polymerization",
      company: "BioHemp Tech",
    },
  ];

  const insights = {
    keyFindings: [
      "CBD products show highest market growth at 94.2% efficiency",
      "Fiber-based products dominate with 234 active product lines",
      "Construction materials sector growing 82.5% year-over-year",
      "Organic certification increases product value by average 35%",
    ],
    recommendations: [
      "Focus on CBD and medical applications for highest ROI",
      "Expand fiber processing capacity to meet textile demand",
      "Invest in construction material R&D for emerging markets",
      "Pursue organic certifications across all product lines",
    ],
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-hemp-green" />
              Analytics & Data Explorer
            </h1>
            <p className="text-marine-text-secondary mt-1">
              Monitor performance metrics, explore data, and discover insights
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFiltersOpen(true)}
              className="border-marine-border text-marine-text-secondary hover:text-white hover:bg-marine-sidebar"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {(filters.plantParts.length > 0 || 
                filters.industries.length > 0 || 
                filters.processingMethods.length > 0 ||
                filters.productStatus !== "all" ||
                filters.priceRange[0] !== 0 || 
                filters.priceRange[1] !== 10000 ||
                filters.sustainabilityScore[0] !== 0 || 
                filters.sustainabilityScore[1] !== 100) && (
                <span className="ml-2 bg-purple-600/20 text-purple-400 px-1.5 py-0.5 rounded-full text-xs">
                  {filters.plantParts.length + 
                   filters.industries.length + 
                   filters.processingMethods.length +
                   (filters.productStatus !== "all" ? 1 : 0) +
                   (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 10000 ? 1 : 0) +
                   (filters.sustainabilityScore[0] !== 0 || filters.sustainabilityScore[1] !== 100 ? 1 : 0)}
                </span>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-marine-border text-marine-text-secondary hover:text-white hover:bg-marine-sidebar"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Analytics Filter Panel */}
        <AnalyticsFilterPanel
          open={filtersOpen}
          onOpenChange={setFiltersOpen}
          filters={filters}
          onFiltersChange={setFilters}
        />
        {/* Metrics Summary */}
        <div className="mb-6">
          <MetricsSummary />
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {kpis.map((kpi, index) => (
            <HempKPICard 
              key={index} 
              {...kpi}
            />
          ))}
        </div>

        {/* Charts Section with Tabs */}
        <Tabs defaultValue="overview" className="mb-6">
          <TabsList className="mb-4 bg-marine-card border-marine-border">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="heatmap">Relationships</TabsTrigger>
            <TabsTrigger value="data">Data Table</TabsTrigger>
            <TabsTrigger value="explorer">Data Explorer</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-[400px] rounded-xl bg-marine-card p-6">
                <ComplexityChart />
              </div>
              <div className="h-[400px] rounded-xl bg-marine-card p-6">
                <ProcessingTimeChart />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="distribution">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-[400px] rounded-xl bg-marine-card p-6">
                <IndustryDistributionChart />
              </div>
              <div className="h-[400px] rounded-xl bg-marine-card p-6">
                <PlantPartUsageChart />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="trends">
            <div className="h-[400px] rounded-xl bg-marine-card p-6">
              <GrowthTrendChart />
            </div>
          </TabsContent>
          
          <TabsContent value="heatmap">
            <div className="h-[500px] rounded-xl bg-marine-card p-6">
              <HeatmapChart />
            </div>
          </TabsContent>
          
          <TabsContent value="data">
            <AnalyticsDataTable />
          </TabsContent>
          
          <TabsContent value="explorer">
            <DataExplorerTab />
          </TabsContent>
        </Tabs>

        {/* Performance Analysis Summary */}
        <div className="mb-6">
          <InsightSummary
            keyFindings={insights.keyFindings}
            recommendations={insights.recommendations}
            className="bg-marine-card border-marine-border"
          />
        </div>

        {/* Product Grid */}
        <h2 className="text-xl font-semibold text-white mb-4">Featured Hemp Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product, index) => (
            <HempProductCard
              key={index}
              {...product}
              onView={() => console.log("View product", index)}
            />
          ))}
        </div>
      </div>
    </AppLayout>
  );
}