import { useState, useMemo } from 'react';
import {
  Building2,
  Globe,
  Grid,
  Search,
  MapPin,
  Calendar,
  Package,
  CheckCircle,
  Eye,
  TrendingUp,
  Users,
  Factory,
  ExternalLink,
  ArrowRight,
  Star,
  Award,
  Filter,
  ArrowUpDown,
  BarChart3,
  PieChart,
  Activity,
  Download
} from 'lucide-react';
import { useCompanies, HempCompany } from '@/hooks/use-companies';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HempCompaniesLeafletMap } from '@/components/hemp-companies-leaflet-map';
import { CompanyDetailModal } from '@/components/company-detail-modal';
import { EnhancedBreadcrumbs } from '@/components/ui/enhanced-breadcrumbs';
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Progress } from '@/components/ui/progress';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { cn } from '@/lib/utils';

// Chart components for statistics
function MiniBarChart({ data, height = 100 }: { data: { label: string; value: number }[], height?: number }) {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className="flex items-end justify-around gap-2" style={{ height }}>
      {data.map((item, index) => (
        <div key={index} className="flex-1 flex flex-col items-center">
          <div 
            className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-500"
            style={{ 
              height: `${(item.value / maxValue) * (height - 20)}px`,
              minHeight: '4px'
            }}
          />
          <span className="text-xs text-gray-400 mt-1">{item.label}</span>
        </div>
      ))}
    </div>
  );
}

function MiniDonutChart({ data, size = 120 }: { data: { label: string; value: number; color: string }[], size?: number }) {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let startAngle = -90;
  
  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg viewBox={`0 0 ${size} ${size}`} className="transform -rotate-90">
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100;
          const angle = (percentage / 100) * 360;
          const endAngle = startAngle + angle;
          
          const x1 = size/2 + (size/2 - 20) * Math.cos((startAngle * Math.PI) / 180);
          const y1 = size/2 + (size/2 - 20) * Math.sin((startAngle * Math.PI) / 180);
          const x2 = size/2 + (size/2 - 20) * Math.cos((endAngle * Math.PI) / 180);
          const y2 = size/2 + (size/2 - 20) * Math.sin((endAngle * Math.PI) / 180);
          
          const largeArc = angle > 180 ? 1 : 0;
          const path = `M ${size/2} ${size/2} L ${x1} ${y1} A ${size/2 - 20} ${size/2 - 20} 0 ${largeArc} 1 ${x2} ${y2} Z`;
          
          startAngle = endAngle;
          
          return (
            <path
              key={index}
              d={path}
              fill={item.color}
              className="hover:opacity-80 transition-opacity"
            />
          );
        })}
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{total}</div>
          <div className="text-xs text-gray-400">Total</div>
        </div>
      </div>
    </div>
  );
}

export default function HempCompaniesEnhanced() {
  const { data: companies = [], isLoading } = useCompanies();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [countryFilter, setCountryFilter] = useState('all');
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [view, setView] = useState<'list' | 'grid'>('grid');
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('list');
  const [sortBy, setSortBy] = useState<string>('name');
  const [verifiedFilter, setVerifiedFilter] = useState<string>('all');

  // Generate alphabet array for filtering
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Add quality score to companies (simulated for now)
  const companiesWithScores = useMemo(() => {
    return companies.map(company => ({
      ...company,
      quality_score: company.quality_score || (
        (company.verified ? 0.3 : 0) +
        (company.website ? 0.2 : 0) +
        (company.description ? 0.2 : 0) +
        (company.company_type ? 0.1 : 0) +
        (company.product_count && company.product_count > 0 ? 0.2 : 0)
      )
    }));
  }, [companies]);

  // Filter and sort companies
  const { filteredCompanies, totalPages, paginatedCompanies } = useMemo(() => {
    if (!companiesWithScores) return { filteredCompanies: [], totalPages: 0, paginatedCompanies: [] };

    let filtered = companiesWithScores.filter(company => {
      const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || company.company_type === typeFilter;
      const matchesCountry = countryFilter === 'all' || company.country === countryFilter;
      const matchesLetter = !selectedLetter || company.name.charAt(0).toUpperCase() === selectedLetter;
      const matchesVerified = verifiedFilter === 'all' || 
        (verifiedFilter === 'verified' && company.verified) ||
        (verifiedFilter === 'unverified' && !company.verified);
      return matchesSearch && matchesType && matchesCountry && matchesLetter && matchesVerified;
    });

    // Sort based on selected option
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'quality':
          return (b.quality_score || 0) - (a.quality_score || 0);
        case 'products':
          return (b.product_count || 0) - (a.product_count || 0);
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return (a.founded_year || 9999) - (b.founded_year || 9999);
        default:
          return 0;
      }
    });

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedCompanies = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredCompanies: filtered, totalPages, paginatedCompanies };
  }, [companiesWithScores, searchTerm, typeFilter, countryFilter, selectedLetter, verifiedFilter, sortBy, currentPage, itemsPerPage]);

  // Get unique countries and types for filters
  const countries = [...new Set(companies.map(c => c.country).filter(Boolean))].sort();
  const companyTypes = [...new Set(companies.map(c => c.company_type).filter(Boolean))].sort();

  // Calculate statistics
  const statistics = useMemo(() => {
    const verifiedCompanies = companiesWithScores.filter(c => c.verified);
    const companiesByType = companyTypes.map(type => ({
      label: type,
      value: companiesWithScores.filter(c => c.company_type === type).length
    }));
    
    const qualityDistribution = [
      { label: 'High', value: companiesWithScores.filter(c => (c.quality_score || 0) >= 0.8).length },
      { label: 'Medium', value: companiesWithScores.filter(c => (c.quality_score || 0) >= 0.5 && (c.quality_score || 0) < 0.8).length },
      { label: 'Low', value: companiesWithScores.filter(c => (c.quality_score || 0) < 0.5).length }
    ];

    const typeDistribution = companyTypes.map((type, index) => ({
      label: type,
      value: companiesWithScores.filter(c => c.company_type === type).length,
      color: ['#3b82f6', '#10b981', '#8b5cf6', '#f59e0b', '#ef4444'][index % 5]
    }));

    return {
      totalCompanies: companiesWithScores.length,
      verifiedCompanies: verifiedCompanies.length,
      totalCountries: countries.length,
      totalProducts: companiesWithScores.reduce((sum, company) => sum + (company.product_count || 0), 0),
      averageQuality: companiesWithScores.reduce((sum, company) => sum + (company.quality_score || 0), 0) / companiesWithScores.length,
      companiesByType,
      qualityDistribution,
      typeDistribution
    };
  }, [companiesWithScores, companyTypes, countries]);

  // Reset page when filters change
  const handleFilterChange = (filterType: string, value: any) => {
    setCurrentPage(1);
    if (filterType === 'search') setSearchTerm(value);
    if (filterType === 'type') setTypeFilter(value);
    if (filterType === 'country') setCountryFilter(value);
    if (filterType === 'letter') setSelectedLetter(value);
    if (filterType === 'verified') setVerifiedFilter(value);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'manufacturer': return 'bg-blue-100 text-blue-800';
      case 'distributor': return 'bg-green-100 text-green-800';
      case 'retailer': return 'bg-purple-100 text-purple-800';
      case 'brand': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return 'from-green-500 to-green-400';
    if (score >= 0.5) return 'from-yellow-500 to-yellow-400';
    return 'from-red-500 to-red-400';
  };

  const handleCompanyClick = (company: HempCompany) => {
    setSelectedCompanyId(company.id);
    setModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900/20">
      <div className="container mx-auto px-4 py-8">
        {/* Enhanced Breadcrumbs */}
        <div className="mb-6">
          <EnhancedBreadcrumbs
            showHome={true}
            showContext={true}
          />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-4 py-2 mb-6">
            <Building2 className="h-4 w-4 text-blue-400" />
            <span className="text-sm text-blue-400 font-medium">Company Directory</span>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            <span className="hemp-brand-secondary">Hemp</span> Companies
            <span className="block text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.3)]">
              Directory
            </span>
          </h1>

          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Discover the global network of hemp companies, from innovative manufacturers
            to cutting-edge retailers shaping the future of sustainable business.
          </p>
        </div>

        {/* Enhanced Statistics Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {/* Total Companies Card */}
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-blue-500/30 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-gray-400">Total Companies</CardTitle>
                <Building2 className="h-4 w-4 text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-400 mb-3">{statistics.totalCompanies}</div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-500">Verified</span>
                  <span className="text-gray-300">{statistics.verifiedCompanies}</span>
                </div>
                <Progress 
                  value={(statistics.verifiedCompanies / statistics.totalCompanies) * 100} 
                  className="h-1.5"
                />
              </div>
            </CardContent>
          </Card>

          {/* Quality Score Distribution */}
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-green-500/30 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-gray-400">Quality Distribution</CardTitle>
                <BarChart3 className="h-4 w-4 text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-400 mb-3">
                {(statistics.averageQuality * 100).toFixed(0)}%
              </div>
              <MiniBarChart data={statistics.qualityDistribution} height={60} />
            </CardContent>
          </Card>

          {/* Company Types */}
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-purple-500/30 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-gray-400">By Type</CardTitle>
                <PieChart className="h-4 w-4 text-purple-400" />
              </div>
            </CardHeader>
            <CardContent className="flex items-center justify-center">
              <MiniDonutChart data={statistics.typeDistribution} size={100} />
            </CardContent>
          </Card>

          {/* Products & Countries */}
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-orange-500/30 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-gray-400">Global Reach</CardTitle>
                <Globe className="h-4 w-4 text-orange-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <div className="text-2xl font-bold text-orange-400">{statistics.totalCountries}</div>
                  <div className="text-xs text-gray-500">Countries</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400">{statistics.totalProducts}</div>
                  <div className="text-xs text-gray-500">Total Products</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Search & Filters */}
        <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
          <div className="space-y-4">
            {/* Search Input - Full Width */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search companies by name or description..."
                value={searchTerm}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
              />
              {searchTerm && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                >
                  ×
                </button>
              )}
            </div>
            
            {/* Enhanced Filters Row */}
            <div className="flex flex-col lg:flex-row lg:items-center gap-3">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm font-medium">Filters:</span>
              </div>
              
              <div className="flex flex-wrap gap-2 flex-1">
                {/* Sort Dropdown */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="quality">Quality Score</SelectItem>
                    <SelectItem value="products">Product Count</SelectItem>
                    <SelectItem value="newest">Recently Added</SelectItem>
                    <SelectItem value="oldest">Established First</SelectItem>
                  </SelectContent>
                </Select>

                {/* Verification Filter */}
                <ToggleGroup 
                  type="single" 
                  value={verifiedFilter} 
                  onValueChange={(value) => value && handleFilterChange('verified', value)}
                  className="bg-gray-800/60 rounded-md p-1"
                >
                  <ToggleGroupItem value="all" className="data-[state=on]:bg-gray-700">
                    All
                  </ToggleGroupItem>
                  <ToggleGroupItem value="verified" className="data-[state=on]:bg-green-600">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Verified
                  </ToggleGroupItem>
                  <ToggleGroupItem value="unverified" className="data-[state=on]:bg-gray-700">
                    Unverified
                  </ToggleGroupItem>
                </ToggleGroup>

                {/* Type Filter */}
                <Select value={typeFilter} onValueChange={(value) => handleFilterChange('type', value)}>
                  <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="all">All Types</SelectItem>
                    {companyTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Country Filter */}
                <Select value={countryFilter} onValueChange={(value) => handleFilterChange('country', value)}>
                  <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                    <SelectValue placeholder="All Countries" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="all">All Countries</SelectItem>
                    {countries.map(country => (
                      <SelectItem key={country} value={country}>{country}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Export Button */}
              <Button variant="outline" className="bg-gray-800/60 border-gray-700/50">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Alphabetical Filter - Modern & Compact */}
        <div className="bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 mb-6 border border-gray-700/30">
          <div className="flex items-center gap-6">
            {/* Label */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-300 font-medium text-sm">A-Z</span>
            </div>

            {/* Centered Letter Pills Container */}
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
                {/* All Button */}
                <button
                  onClick={() => handleFilterChange('letter', null)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                    selectedLetter === null
                      ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                      : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
                  }`}
                >
                  All
                </button>

                {/* Letter Pills */}
                {alphabet.map(letter => (
                  <button
                    key={letter}
                    onClick={() => handleFilterChange('letter', letter)}
                    className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                      selectedLetter === letter
                        ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                        : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                    }`}
                  >
                    {letter}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Results Summary and View Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="text-gray-400 text-sm">
            Showing {paginatedCompanies.length} of {filteredCompanies.length} companies
          </div>

          <div className="flex items-center gap-2">
            <span className="text-gray-400 text-sm">Items per page:</span>
            <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
              <SelectTrigger className="w-20 bg-gray-800/60 border-gray-700/50 text-gray-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="6">6</SelectItem>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="24">24</SelectItem>
                <SelectItem value="48">48</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex justify-end mb-6">
            <TabsList className="bg-black/40">
              <TabsTrigger value="list" className="flex items-center gap-2">
                <Grid className="h-4 w-4" />
                List View
              </TabsTrigger>
              <TabsTrigger value="map" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Map View
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="list" className="mt-0">
            <div className="flex justify-between items-center mb-4">
              <p className="text-gray-300">
                Showing {filteredCompanies.length} of {companies.length} companies
              </p>
              <div className="flex gap-2">
                <Button
                  variant={view === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('grid')}
                  className={view === 'grid' ? 'bg-green-600' : ''}
                >
                  Grid
                </Button>
                <Button
                  variant={view === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('list')}
                  className={view === 'list' ? 'bg-green-600' : ''}
                >
                  List
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
              </div>
            ) : view === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedCompanies.map((company) => (
                  <Card
                    key={company.id}
                    className="group relative overflow-hidden bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-blue-500/30 hover:bg-gray-900/60 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-1 cursor-pointer"
                    onClick={() => {
                      setSelectedCompanyId(company.id);
                      setModalOpen(true);
                    }}
                  >
                    {/* Quality Score Bar */}
                    <div className="absolute top-0 left-0 right-0 h-1 bg-gray-800">
                      <div 
                        className={cn(
                          "h-full bg-gradient-to-r transition-all duration-500",
                          getQualityColor(company.quality_score || 0.5)
                        )}
                        style={{width: `${(company.quality_score || 0.5) * 100}%`}}
                      />
                    </div>

                    {/* Header with gradient background */}
                    <div className="bg-gradient-to-r from-blue-800/20 via-blue-600/10 to-blue-500/20 p-6 pb-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {company.verified && (
                            <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">
                              <Award className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                          {company.company_type && (
                            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50 text-xs">
                              {company.company_type}
                            </Badge>
                          )}
                        </div>
                        {company.logo_url ? (
                          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                            <img
                              src={company.logo_url}
                              alt={company.name}
                              className="h-16 w-16 object-contain"
                            />
                          </div>
                        ) : (
                          <div className="h-20 w-20 rounded-full bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center text-2xl font-bold text-white">
                            {company.name.charAt(0)}
                          </div>
                        )}
                      </div>

                      <CardTitle className="text-2xl font-heading font-bold hemp-brand-ultra line-clamp-2 mb-2">
                        {company.name}
                      </CardTitle>

                      {company.website && (
                        <div className="flex items-center gap-2 text-sm text-gray-300">
                          <Globe className="h-4 w-4 text-blue-400" />
                          <span className="line-clamp-1">{company.website}</span>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-6 pt-4">
                      {/* Quality Score Display */}
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-xs text-gray-400">Quality Score</span>
                        <div className="flex items-center gap-2">
                          <div className="flex gap-0.5">
                            {[...Array(5)].map((_, i) => (
                              <div
                                key={i}
                                className={cn(
                                  "h-2 w-2 rounded-full",
                                  i < Math.round((company.quality_score || 0.5) * 5)
                                    ? "bg-green-500"
                                    : "bg-gray-700"
                                )}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium text-gray-300">
                            {Math.round((company.quality_score || 0.5) * 100)}%
                          </span>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-sm text-gray-300 leading-relaxed line-clamp-3 mb-4">
                        {company.description || 'No description available'}
                      </p>

                      {/* Company info */}
                      <div className="flex items-center gap-4 mb-4 text-xs text-gray-400">
                        {company.country && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>{company.country}</span>
                          </div>
                        )}
                        {company.founded_year && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>Est. {company.founded_year}</span>
                          </div>
                        )}
                        {company.product_count !== undefined && (
                          <div className="flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            <span>{company.product_count} products</span>
                          </div>
                        )}
                      </div>

                      {/* Stats Row */}
                      <div className="grid grid-cols-3 gap-2 p-3 bg-gray-900/50 rounded-lg -mx-2">
                        <div className="text-center">
                          <div className="text-lg font-bold text-green-400">{company.product_count || 0}</div>
                          <div className="text-xs text-gray-500">Products</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-blue-400">
                            {company.founded_year || '—'}
                          </div>
                          <div className="text-xs text-gray-500">Founded</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-purple-400">
                            {company.verified ? '✓' : '○'}
                          </div>
                          <div className="text-xs text-gray-500">Verified</div>
                        </div>
                      </div>

                      {/* Footer with action button */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-800/50 mt-4">
                        <div className="flex items-center gap-2">
                          {company.website && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-blue-400 p-0 h-auto"
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(company.website, '_blank');
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-400 hover:text-blue-300 opacity-0 group-hover:opacity-100 transition-all hover:bg-blue-900/20"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedCompanyId(company.id);
                            setModalOpen(true);
                          }}
                        >
                          <span className="text-xs mr-2">View Details</span>
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {paginatedCompanies.map((company) => (
                  <Card 
                    key={company.id} 
                    className="relative bg-black/40 border-gray-700 hover:border-green-400 transition-colors cursor-pointer overflow-hidden"
                    onClick={() => {
                      setSelectedCompanyId(company.id);
                      setModalOpen(true);
                    }}
                  >
                    {/* Quality Score Bar */}
                    <div className="absolute top-0 left-0 right-0 h-1 bg-gray-800">
                      <div 
                        className={cn(
                          "h-full bg-gradient-to-r transition-all duration-500",
                          getQualityColor(company.quality_score || 0.5)
                        )}
                        style={{width: `${(company.quality_score || 0.5) * 100}%`}}
                      />
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-semibold hemp-brand-ultra flex items-center gap-2">
                              {company.name}
                              {company.verified && (
                                <CheckCircle className="h-5 w-5 text-green-400" />
                              )}
                            </h3>
                            <div className="flex gap-2">
                              {company.company_type && (
                                <Badge className={getTypeColor(company.company_type)}>
                                  {company.company_type}
                                </Badge>
                              )}
                            </div>
                            <div className="ml-auto flex items-center gap-2">
                              <span className="text-sm text-gray-400">Quality:</span>
                              <span className="text-sm font-medium text-gray-300">
                                {Math.round((company.quality_score || 0.5) * 100)}%
                              </span>
                            </div>
                          </div>
                          <p className="text-gray-300 mb-3">
                            {company.description || 'No description available'}
                          </p>
                          <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                            {company.website && (
                              <a
                                href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-green-400 transition-colors"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {company.website}
                              </a>
                            )}
                            {company.country && (
                              <span className="flex items-center">
                                <MapPin className="h-4 w-4 mr-1" />
                                {company.country}
                              </span>
                            )}
                            {company.founded_year && (
                              <span className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                Founded {company.founded_year}
                              </span>
                            )}
                            {company.product_count !== undefined && (
                              <span className="flex items-center">
                                <Package className="h-4 w-4 mr-1" />
                                {company.product_count} products
                              </span>
                            )}
                          </div>
                        </div>
                        {company.logo_url ? (
                          <img
                            src={company.logo_url}
                            alt={company.name}
                            className="h-20 w-20 rounded-lg object-contain ml-4 bg-white/5 p-2"
                          />
                        ) : (
                          <div className="h-20 w-20 rounded-full bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center text-2xl font-bold text-white ml-4">
                            {company.name.charAt(0)}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 pt-6 border-t border-gray-800/50">
                <div className="text-gray-400 text-sm">
                  Page {currentPage} of {totalPages}
                </div>

                <div className="flex items-center gap-2">
                  {/* Previous Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page Numbers */}
                  <div className="flex gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 ${
                            currentPage === pageNum
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
                          }`}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  {/* Next Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="map" className="mt-0">
            <Card className="bg-black/40 border-gray-700">
              <CardContent className="p-0">
                {isLoading ? (
                  <div className="text-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
                  </div>
                ) : (
                  <div className="relative">
                    <HempCompaniesLeafletMap
                      companies={filteredCompanies}
                      onCompanyClick={(company) => {
                        setSelectedCompanyId(company.id);
                        setModalOpen(true);
                      }}
                    />
                    <div className="absolute bottom-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700">
                      <p className="text-sm text-gray-300">
                        Showing {filteredCompanies.filter(c => c.latitude && c.longitude).length} companies with location data
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      
      <CompanyDetailModal
        companyId={selectedCompanyId}
        open={modalOpen}
        onOpenChange={setModalOpen}
      />
    </div>
  );
}