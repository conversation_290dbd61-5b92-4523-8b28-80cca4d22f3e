import { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { KPICard } from "@/components/ui/kpi-card";
import { DataCard } from "@/components/ui/data-card";
import { InsightSummary } from "@/components/ui/insight-summary";
import { FilterState } from "@/components/layout/FiltersDrawer";
import { 
  Package, 
  TrendingUp, 
  Users, 
  Leaf,
  BarChart3,
  Activity
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getHempProducts } from "@/lib/api";

// Example chart component (placeholder)
function AnalyticsChart() {
  return (
    <div className="h-[400px] rounded-xl bg-black/50 backdrop-blur-sm border border-gray-800 p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Market Growth Analysis</h3>
      <div className="h-full flex items-center justify-center text-gray-500">
        <BarChart3 className="h-16 w-16 opacity-20" />
        <p className="ml-4">Chart visualization will go here</p>
      </div>
    </div>
  );
}

export default function Dashboard() {
  const [filters, setFilters] = useState<FilterState>();
  
  // Fetch products with filters
  const { data: products = [] } = useQuery({
    queryKey: ["products", filters],
    queryFn: () => getHempProducts(),
  });

  // Example KPI data
  const kpis = [
    {
      title: "Total Products",
      value: "714",
      change: { value: 12.5, label: "vs last month" },
      icon: Package,
      trend: "up" as const,
    },
    {
      title: "Active Companies",
      value: "482",
      change: { value: 8.2, label: "vs last month" },
      icon: Users,
      trend: "up" as const,
    },
    {
      title: "Avg Carbon Offset",
      value: "42%",
      change: { value: -2.1, label: "vs last month" },
      icon: Leaf,
      trend: "down" as const,
    },
    {
      title: "Market Growth",
      value: "$2.4B",
      change: { value: 18.7, label: "YoY" },
      icon: TrendingUp,
      trend: "up" as const,
    },
  ];

  // Example insights
  const insights = {
    keyFindings: [
      "Hemp fiber products show 35% higher adoption in construction industry",
      "European markets lead with 62% of global hemp textile production",
      "Carbon sequestration rates average 1.62 tons CO₂ per hectare",
      "Medical applications growing at 24% CAGR through 2025",
    ],
    recommendations: [
      "Focus on B2B partnerships in construction sector for Q1 2025",
      "Expand textile product lines to capture European demand",
      "Invest in carbon credit certification for agricultural products",
      "Develop specialized medical-grade processing capabilities",
    ],
  };

  return (
    <DashboardLayout showFilters onFiltersChange={setFilters}>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">Hemp Industry Overview</h1>
          <p className="text-gray-400 mt-1">
            Real-time analytics and insights for the global hemp market
          </p>
        </div>

        {/* KPI Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {kpis.map((kpi, index) => (
            <KPICard key={index} {...kpi} />
          ))}
        </div>

        {/* Analytics Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AnalyticsChart />
          <AnalyticsChart />
        </div>

        {/* AI Insights */}
        <InsightSummary
          keyFindings={insights.keyFindings}
          recommendations={insights.recommendations}
        />

        {/* Recent Products */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Recent Products</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {products.slice(0, 6).map((product: any) => (
              <DataCard
                key={product.id}
                title={product.name}
                subtitle={product.description}
                thumbnail={product.primary_image_url}
                metadata={[
                  { label: "Plant Part", value: product.plant_part_name || "N/A" },
                  { label: "Industry", value: product.sub_industry_name || "N/A" },
                  { label: "Company", value: product.company_name || "Unknown" },
                ]}
                badges={[
                  { label: product.processing_method || "Standard", variant: "success" },
                ]}
                href={`/products/${product.id}`}
              />
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}