import { Helmet } from "react-helmet";
import ProductsExplorer from "@/components/products/ProductsExplorer";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";

const ProductsExplorerPage = () => {
  return (
    <>
      <Helmet>
        <title>Products Explorer - HempQuarterz® Database</title>
        <meta
          name="description"
          content="Explore 5,196+ hemp products with advanced filtering, visualization, and analytics. Discover by industry, plant part, company, or stage."
        />
      </Helmet>

      <div className="min-h-screen bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Link href="/products">
              <a className="inline-flex items-center text-gray-400 hover:text-white transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Simple View
              </a>
            </Link>
          </div>

          {/* Main Explorer Component */}
          <ProductsExplorer initialView="grid" />
        </div>
      </div>
    </>
  );
};

export default ProductsExplorerPage;