import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { AppLayout } from "@/components/layout/AppLayout";
import { SmartSearch } from "@/components/ui/smart-search";
import { SearchFilters } from "@/components/ui/search-filters";
import { SearchHistory } from "@/components/ui/search-history";
import ProductCard from "@/components/product/product-card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter, 
  LayoutGrid, 
  List,
  SortAsc,
  Save
} from "lucide-react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { cn } from "@/lib/utils";

export default function EnhancedSearchPage() {
  const [location, setLocation] = useLocation();
  const { data: products, isLoading } = useAllHempProducts();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const [showFilters, setShowFilters] = useState(true);
  const [showHistory, setShowHistory] = useState(false);
  
  // Get search params from URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const query = params.get('q') || params.get('search') || '';
    if (query) {
      setSearchQuery(query);
    }
  }, []);

  // Filter products based on search and filters
  const filteredProducts = products?.filter(product => {
    // Search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        product.name.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query) ||
        product.benefits_advantages?.toLowerCase().includes(query);
      
      if (!matchesSearch) return false;
    }

    // Plant parts filter
    if (filters.plantParts?.length > 0) {
      if (!filters.plantParts.includes(product.plant_part_id)) {
        return false;
      }
    }

    // Industries filter
    if (filters.industries?.length > 0) {
      if (!filters.industries.includes(product.industry_id)) {
        return false;
      }
    }

    // Stage filter
    if (filters.stage && filters.stage !== 'all') {
      if (product.commercialization_stage?.toLowerCase() !== filters.stage) {
        return false;
      }
    }

    return true;
  }) || [];

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'newest':
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
      case 'relevance':
      default:
        // Simple relevance: exact matches first
        const aExact = a.name.toLowerCase() === searchQuery.toLowerCase();
        const bExact = b.name.toLowerCase() === searchQuery.toLowerCase();
        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;
        return 0;
    }
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setShowHistory(false);
    
    // Update URL
    const params = new URLSearchParams(window.location.search);
    if (query) {
      params.set('q', query);
    } else {
      params.delete('q');
    }
    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);

    // Add to history
    if ((window as any).addSearchToHistory && query) {
      (window as any).addSearchToHistory({
        query,
        resultCount: filteredProducts.length,
        filters
      });
    }
  };

  const handleSearchHistorySelect = (item: any) => {
    setSearchQuery(item.query);
    if (item.filters) {
      setFilters(item.filters);
    }
    setShowHistory(false);
  };


  const saveSearch = () => {
    if ((window as any).addSearchToHistory && searchQuery) {
      (window as any).addSearchToHistory({
        query: searchQuery,
        resultCount: filteredProducts.length,
        filters,
        isSaved: true
      });
    }
  };

  return (
    <AppLayout>
      <div className="min-h-screen bg-marine-bg">
        {/* Enhanced Search Header */}
        <div className="bg-marine-sidebar border-b border-marine-border sticky top-16 z-30">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search Bar */}
              <div className="flex-1">
                <SmartSearch
                  placeholder="Search products, companies, or ask a question..."
                  onSearch={handleSearch}
                  className="w-full"
                  showVoiceSearch={true}
                  showImageSearch={false}
                  showAISuggestions={true}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className={cn(
                    "gap-2",
                    Object.keys(filters).some(k => filters[k]?.length > 0 || (filters[k] && filters[k] !== 'all')) && "border-green-500"
                  )}
                >
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistory(!showHistory)}
                >
                  <Search className="h-4 w-4" />
                  History
                </Button>

                <div className="border-l border-marine-border pl-2 flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className={viewMode === 'grid' ? 'bg-marine-card' : ''}
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className={viewMode === 'list' ? 'bg-marine-card' : ''}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                <label htmlFor="sort-select" className="sr-only">Sort results by</label>
                <select
                  id="sort-select"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-1.5 bg-marine-card border border-marine-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  aria-label="Sort results"
                >
                  <option value="relevance">Relevance</option>
                  <option value="name">Name (A-Z)</option>
                  <option value="newest">Newest First</option>
                </select>

                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={saveSearch}
                    className="gap-2"
                  >
                    <Save className="h-4 w-4" />
                    Save
                  </Button>
                )}

              </div>
            </div>

            {/* Results Summary */}
            {searchQuery && (
              <div className="mt-3 flex items-center gap-2 text-sm">
                <span className="text-marine-text-secondary">
                  Found <span className="text-white font-medium">{sortedProducts.length}</span> results
                  {searchQuery && (
                    <> for "<span className="text-green-400">{searchQuery}</span>"</>
                  )}
                </span>
                {Object.keys(filters).some(k => filters[k]?.length > 0 || (filters[k] && filters[k] !== 'all')) && (
                  <Badge variant="secondary" className="ml-2">
                    Filtered
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex gap-6">
            {/* Sidebar */}
            <aside className={cn(
              "w-80 flex-shrink-0",
              !showFilters && !showHistory && "hidden"
            )}>
              {showHistory ? (
                <SearchHistory
                  onSelectSearch={handleSearchHistorySelect}
                />
              ) : (
                <SearchFilters
                  onFiltersChange={setFilters}
                />
              )}
            </aside>

            {/* Results */}
            <div className="flex-1">
              {isLoading ? (
                <div className="text-center py-12">
                  <div className="inline-flex items-center gap-2 text-marine-text-secondary">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                    Loading products...
                  </div>
                </div>
              ) : sortedProducts.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="h-16 w-16 mx-auto mb-4 text-marine-text-secondary opacity-50" />
                  <h3 className="text-xl font-semibold mb-2">No products found</h3>
                  <p className="text-marine-text-secondary">
                    Try adjusting your search or filters
                  </p>
                </div>
              ) : viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {sortedProducts.map(product => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      variant="detailed"
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {sortedProducts.map(product => (
                    <div
                      key={product.id}
                      className="bg-marine-card rounded-lg p-4 hover:bg-marine-card/80 transition-colors"
                    >
                      <div className="flex items-start gap-4">
                        {product.image_url && (
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-24 h-24 object-cover rounded-lg"
                          />
                        )}
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white mb-1">
                            {product.name}
                          </h3>
                          <p className="text-marine-text-secondary text-sm mb-2 line-clamp-2">
                            {product.description}
                          </p>
                          <div className="flex items-center gap-4 text-sm">
                            <Badge variant="secondary">
                              {product.plant_part_name}
                            </Badge>
                            <span className="text-marine-text-secondary">
                              {product.industry_name}
                            </span>
                            {product.commercialization_stage && (
                              <Badge variant="outline">
                                {product.commercialization_stage}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setLocation(`/product/${product.id}`)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination placeholder */}
              {sortedProducts.length > 50 && (
                <div className="mt-8 flex justify-center">
                  <p className="text-marine-text-secondary text-sm">
                    Showing {Math.min(50, sortedProducts.length)} of {sortedProducts.length} results
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}