import { lazy, Suspense, useEffect } from "react";
import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "@/lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ErrorBoundary } from "@/components/error-boundary";
import { AuthProvider } from "@/components/auth/auth-provider";
import { EnhancedAuthProvider } from "@/components/auth/enhanced-auth-provider";
import NotFound from "@/pages/not-found";
import { OptimizedParticleBackground } from "@/components/layout/OptimizedParticleBackground";
import { LayoutWrapper } from "@/components/layout/LayoutWrapper";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { performanceMonitor } from "@/lib/performance-monitor";
import { useGlobalKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { KeyboardShortcutsModal } from "@/components/ui/keyboard-shortcuts-modal";

// Simple redirect component
function Redirect({ to }: { to: string }) {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation(to);
  }, [to, setLocation]);

  return null;
}

// Lazy load all route components
const HomePage = lazy(() => import("@/pages/home"));
const AboutPage = lazy(() => import("@/pages/about"));
const PlantPartsPage = lazy(() => import("@/pages/plant-parts"));
const PlantTypePage = lazy(() => import("@/pages/plant-type"));
const PlantPartPage = lazy(() => import("@/pages/plant-part"));
const ProductDetailPage = lazy(() => import("@/pages/product-detail-simplified"));
const IndustriesPage = lazy(() => import("@/pages/industries"));
const ResearchPage = lazy(() => import("@/pages/research"));
const ResearchDetailPage = lazy(() => import("@/pages/research-detail"));
const AllProductsPage = lazy(() => import("@/pages/all-products-simplified"));
const HempDexUnified = lazy(() => import("@/pages/hemp-dex-unified"));
const SupabaseTest = lazy(() => import("@/components/supabase-test"));
const SupabaseIndustries = lazy(() => import("@/components/supabase-industries"));
const SupabaseTestConnection = lazy(() => import("@/components/supabase-test-connection"));
const DebugSupabase = lazy(() => import("@/pages/debug-supabase"));
const AdminPage = lazy(() => import("@/pages/admin"));
const AdminDashboardRedesigned = lazy(() => import("@/pages/admin-dashboard-redesigned"));
const AdminSettings = lazy(() => import("@/pages/admin-settings"));
const LoginPage = lazy(() => import("@/pages/login"));
const RegisterPage = lazy(() => import("@/pages/register"));
const EnhancedLoginPage = lazy(() => import("@/pages/enhanced-login"));
const DashboardPage = lazy(() => import("@/pages/dashboard"));
const HempAnalyticsDashboardPage = lazy(() => import("./pages/hemp-analytics-dashboard"));
const EnhancedRegisterPage = lazy(() => import("@/pages/enhanced-register"));
const AuthCallbackPage = lazy(() => import("@/pages/auth-callback"));
const PokedexDemo = lazy(() => import("@/pages/pokedex-demo"));
const HempCompanies = lazy(() => import("@/pages/hemp-companies-enhanced"));
const UXShowcase = lazy(() => import("@/pages/ux-showcase"));
const EnhancedSearchPage = lazy(() => import("@/pages/enhanced-search"));

function Router() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Switch data-oid="mz1m0k7">
        <Route path="/" component={HomePage} data-oid=".stzk2_" />
      <Route path="/dashboard" component={HempAnalyticsDashboardPage} data-oid="dashboard" />
      <Route path="/about" component={AboutPage} data-oid="8cc79-4" />
      <Route
        path="/plant-parts"
        component={PlantPartsPage}
        data-oid="p8o_m6_"
      />


      <Route
        path="/plant-type/:id"
        component={PlantTypePage}
        data-oid="-768tu8"
      />

      <Route
        path="/plant-part/:id"
        component={PlantPartPage}
        data-oid="wb_chhl"
      />

      {/* Redirect /plant-part (without ID) to /plant-parts */}
      <Route
        path="/plant-part"
        component={() => <Redirect to="/plant-parts" />}
      />

      <Route
        path="/product/:id"
        component={ProductDetailPage}
        data-oid="q_cf4tj"
      />

      {/* Redirect /product (without ID) to /products */}
      <Route
        path="/product"
        component={() => <Redirect to="/products" />}
      />

      <Route path="/industries" component={IndustriesPage} data-oid="i4mqmig" />
      <Route path="/hemp-companies" component={HempCompanies} data-oid="hemp-companies" />
      <Route path="/companies" component={() => <Redirect to="/hemp-companies" />} />
      {/* Products - Single consolidated route */}
      <Route path="/products" component={AllProductsPage} data-oid="products" />
      <Route path="/hemp-dex-unified" component={HempDexUnified} data-oid="hemp-dex-unified" />
      <Route path="/search" component={EnhancedSearchPage} data-oid="enhanced-search" />

      {/* Legacy redirects */}
      <Route path="/hemp-dex" component={() => { window.location.href = "/hemp-dex-unified"; return null; }} />
      <Route path="/hemp-dex-old" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/hemp-dex-enhanced" component={() => { window.location.href = "/hemp-dex-unified"; return null; }} />
      <Route path="/all-products" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/products-by-category" component={() => { window.location.href = "/hemp-dex-unified?tab=plant-parts"; return null; }} />
      <Route path="/product-listing" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/research" component={ResearchPage} data-oid="r71_flj" />
      <Route
        path="/research/:paperId"
        component={ResearchDetailPage}
        data-oid="foofk03"
      />

      <Route
        path="/supabase-test"
        component={SupabaseTest}
        data-oid="n.xojo2"
      />

      <Route
        path="/supabase-industries"
        component={SupabaseIndustries}
        data-oid="i7ewdxm"
      />

      <Route
        path="/supabase-connection"
        component={SupabaseTestConnection}
        data-oid="80q9r5w"
      />

      <Route path="/debug" component={DebugSupabase} data-oid="1-dlpt_" />
      <Route path="/debug-supabase" component={DebugSupabase} data-oid="debug-sup" />
      <Route path="/admin" component={AdminPage} data-oid="admin-page" />
      <Route path="/admin-dashboard" component={AdminDashboardRedesigned} data-oid="admin-dashboard" />
      <Route path="/admin-settings" component={AdminSettings} data-oid="admin-settings" />
      <Route path="/login" component={LoginPage} data-oid="login" />
      <Route path="/register" component={RegisterPage} data-oid="register" />
      <Route path="/enhanced-login" component={EnhancedLoginPage} data-oid="enhanced-login" />
      <Route path="/enhanced-register" component={EnhancedRegisterPage} data-oid="enhanced-register" />
      <Route path="/auth/callback" component={AuthCallbackPage} data-oid="auth-callback" />
      <Route path="/pokedex-demo" component={PokedexDemo} data-oid="pokedex-demo" />
      <Route path="/ux-showcase" component={UXShowcase} data-oid="ux-showcase" />
      <Route component={NotFound} data-oid="94.5p89" />
      </Switch>
    </Suspense>
  );
}

function AppContent() {
  // Enable global keyboard shortcuts
  useGlobalKeyboardShortcuts();
  
  return (
    <>
      <div className="min-h-screen text-gray-100 relative" data-oid="9aq6v3f">
        <OptimizedParticleBackground quality="medium" />
        <LayoutWrapper>
          <Router data-oid="geelxlm" />
        </LayoutWrapper>
      </div>
      <KeyboardShortcutsModal />
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient} data-oid="u-8-pob">
        <EnhancedAuthProvider>
          <TooltipProvider data-oid="0n1weth">
            <AppContent />
            <Toaster data-oid="yh70n6c" />
          </TooltipProvider>
        </EnhancedAuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
