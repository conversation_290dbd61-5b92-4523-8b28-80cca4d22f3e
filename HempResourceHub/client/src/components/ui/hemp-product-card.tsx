import { cn } from "@/lib/utils";
import { Eye, Download, Leaf } from "lucide-react";

interface HempProductCardProps {
  name: string;
  category: string;
  plantPart: string;
  price: string;
  sustainabilityScore: number;
  processingMethod?: string;
  company?: string;
  thumbnail?: string;
  onView?: () => void;
  onDownload?: () => void;
  className?: string;
}

export function HempProductCard({
  name,
  category,
  plantPart,
  price,
  sustainabilityScore,
  processingMethod,
  company,
  thumbnail,
  onView,
  onDownload,
  className
}: HempProductCardProps) {
  return (
    <div className={cn(
      "rounded-xl bg-marine-card overflow-hidden hover:bg-marine-cardHover transition-colors group",
      className
    )}>
      {/* Thumbnail with overlay */}
      <div className="relative h-48 bg-marine-bg">
        {thumbnail ? (
          <img 
            src={thumbnail} 
            alt={name}
            className="h-full w-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-green-900/20 to-green-600/20">
            <Leaf className="h-16 w-16 text-green-500/30" />
          </div>
        )}
        
        {/* Category badge */}
        <div className="absolute top-3 left-3">
          <span className="px-3 py-1 rounded-full bg-marine-bg/80 backdrop-blur-sm text-xs font-medium text-white">
            {category}
          </span>
        </div>
        
        {/* Sustainability score */}
        <div className="absolute top-3 right-3">
          <div className="flex items-center gap-1 bg-marine-bg/80 backdrop-blur-sm px-2 py-1 rounded">
            <Leaf className="h-3 w-3 text-green-500" />
            <span className="text-sm font-medium text-green-500">{sustainabilityScore}%</span>
          </div>
        </div>
      </div>

      {/* Details */}
      <div className="p-4 space-y-3">
        <div>
          <h3 className="font-medium text-white line-clamp-1">{name}</h3>
          {company && (
            <p className="text-sm text-marine-text-muted mt-1">{company}</p>
          )}
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-marine-text-secondary">Plant Part:</span>
            <p className="text-white font-medium">{plantPart}</p>
          </div>
          
          <div>
            <span className="text-marine-text-secondary">Price:</span>
            <p className="text-white font-medium">{price}</p>
          </div>
        </div>
        
        {processingMethod && (
          <div className="text-sm">
            <span className="text-marine-text-secondary">Processing:</span>
            <p className="text-white">{processingMethod}</p>
          </div>
        )}
        
        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <button
            onClick={onView}
            className="flex-1 flex items-center justify-center gap-2 rounded-lg bg-green-600/10 px-3 py-2 text-sm font-medium text-green-500 hover:bg-green-600/20 transition-colors"
          >
            <Eye className="h-4 w-4" />
            View Details
          </button>
          
          <button
            onClick={onDownload}
            className="flex items-center justify-center rounded-lg bg-marine-border px-3 py-2 text-marine-text-secondary hover:bg-marine-border/80 hover:text-white transition-colors"
          >
            <Download className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}