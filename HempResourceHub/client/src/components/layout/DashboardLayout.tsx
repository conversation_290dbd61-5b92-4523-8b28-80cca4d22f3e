import { ReactNode, useState } from "react";
import { Sidebar } from "./Sidebar";
import { HeaderBar } from "./HeaderBar";
import { FiltersDrawer, FilterState } from "./FiltersDrawer";
import { MainPane } from "./MainPane";

interface DashboardLayoutProps {
  children: ReactNode;
  showFilters?: boolean;
  onFiltersChange?: (filters: FilterState) => void;
}

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  technologyReadiness: "all",
  marketSize: [0, 1000000],
  carbonOffset: [0, 100],
  yearRange: [2020, 2025],
};

export function DashboardLayout({ 
  children, 
  showFilters = false,
  onFiltersChange 
}: DashboardLayoutProps) {
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>(defaultFilters);

  const handleFiltersChange = (newFilters: FilterState) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  return (
    <div className="flex h-screen bg-marine-bg">
      <Sidebar />
      
      <div className="flex flex-1 flex-col">
        <HeaderBar />
        
        <div className="relative flex flex-1 overflow-hidden">
          {showFilters && (
            <FiltersDrawer
              open={filtersOpen}
              onOpenChange={setFiltersOpen}
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          )}
          
          <MainPane className="bg-marine-bg">
            {children}
          </MainPane>
        </div>
      </div>
    </div>
  );
}