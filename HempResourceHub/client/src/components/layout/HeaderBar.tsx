import { useLocation } from "wouter";
import { Search, User, Bell, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface HeaderBarProps {
  totalRecords?: number;
}

const pageNames: Record<string, string> = {
  "/": "Dashboard",
  "/dashboard": "Hemp Industry Overview",
  "/map": "Global Hemp Production Map",
  "/analytics": "Industry Analytics",
  "/products": "Product Categories",
  "/data": "Hemp Data Explorer",
  "/admin": "Admin Panel",
  "/research": "Research Database",
  "/companies": "Company Directory",
};

export function HeaderBar({ totalRecords = 714 }: HeaderBarProps) {
  const [location] = useLocation();
  const { theme, setTheme } = useTheme();
  const [searchOpen, setSearchOpen] = useState(false);

  const currentPage = pageNames[location] || "Hemp Resource Hub";

  return (
    <header className="h-20 bg-marine-bg border-b border-marine-border">
      <div className="flex h-full items-center justify-between px-6">
        {/* Page Title */}
        <div>
          <h2 className="text-2xl font-semibold text-white">{currentPage}</h2>
          <p className="text-sm text-marine-text-secondary mt-1">Industrial Hemp Analytics Dashboard</p>
        </div>

        {/* Center Search (hidden on mobile) */}
        <div className="hidden lg:block flex-1 max-w-xl mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="search"
              placeholder="Search products, companies, research..."
              className="w-full rounded-lg bg-marine-card pl-10 pr-4 py-2 text-sm text-white placeholder-marine-text-muted border border-marine-border focus:border-green-500 focus:outline-none focus:ring-1 focus:ring-green-500/50"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-4">
          {/* Total Records KPI */}
          <div className="hidden sm:flex items-center gap-2 px-6 py-3 rounded-lg bg-marine-card">
            <span className="text-sm text-marine-text-secondary">Total Products</span>
            <span className="text-2xl font-bold text-white">{totalRecords}</span>
          </div>

          {/* Mobile Search Toggle */}
          <button
            onClick={() => setSearchOpen(!searchOpen)}
            className="lg:hidden p-2 text-gray-400 hover:text-white transition-colors"
          >
            <Search className="h-5 w-5" />
          </button>

        </div>
      </div>

      {/* Mobile Search Drawer */}
      {searchOpen && (
        <div className="lg:hidden border-t border-gray-800 p-4 bg-black/50">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="search"
              placeholder="Search..."
              className="w-full rounded-lg bg-gray-800/50 pl-10 pr-4 py-2 text-sm text-white placeholder-gray-400 border border-gray-700 focus:border-green-500 focus:outline-none"
              autoFocus
            />
          </div>
        </div>
      )}
    </header>
  );
}