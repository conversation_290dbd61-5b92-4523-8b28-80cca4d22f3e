import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

interface NavLink {
  href: string;
  label: string;
  children?: NavLink[];
}

const mainNavLinks: NavLink[] = [
  { href: "/", label: "Home" },
  { href: "/about", label: "About" },
  { href: "/plant-parts", label: "Plant Parts" },
  { href: "/industries", label: "Industries" },
  { href: "/hemp-companies", label: "Companies" },
  { href: "/research", label: "Research" },
  { href: "/products", label: "Products" },
];

export const NavLinks = () => {
  const [location] = useLocation();

  const isActive = (href: string) => {
    if (href === "/") return location === href;
    return location.startsWith(href);
  };

  return (
    <div className="flex items-center space-x-1">
      {mainNavLinks.map((link) => (
        <Link key={link.href} href={link.href}>
          <div
            className={cn(
              "px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
              "hover:bg-gray-800 hover:text-purple-400",
              isActive(link.href)
                ? "text-purple-400 bg-gray-800/50 border-b-2 border-purple-400"
                : "text-white border-b-2 border-transparent"
            )}
          >
            {link.label}
          </div>
        </Link>
      ))}
    </div>
  );
};

export const MobileNavLinks = ({ onNavigate }: { onNavigate?: () => void }) => {
  const [location] = useLocation();

  const isActive = (href: string) => {
    if (href === "/") return location === href;
    return location.startsWith(href);
  };

  return (
    <nav className="flex flex-col space-y-4">
      {mainNavLinks.map((link) => (
        <Link key={link.href} href={link.href}>
          <div
            className={cn(
              "px-3 py-2 text-xl cursor-pointer transition-colors duration-200",
              isActive(link.href)
                ? "text-purple-400 font-medium"
                : "text-white hover:text-purple-400"
            )}
            onClick={onNavigate}
          >
            {link.label}
          </div>
        </Link>
      ))}
    </nav>
  );
};