import { <PERSON> } from "wouter";
import { HempProduct, PlantPart, Industry } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Leaf, TreePine, Factory, Building2, CheckCircle, Sparkles } from "lucide-react";
import { useState } from "react";

interface EnhancedProductCardProps {
  product: HempProduct & { image_url?: string };
  plantParts?: PlantPart[];
  industries?: Industry[];
  viewMode?: 'grid' | 'list' | 'mobile';
}

const EnhancedProductCard = ({ product, plantParts, industries, viewMode = 'grid' }: EnhancedProductCardProps) => {
  const [imageError, setImageError] = useState(false);
  
  // Extract stage from product properties
  const stage = product.commercializationStage || 'Research';
  
  const stageColors = {
    'Growing': 'bg-green-500/20 text-green-400 border-green-500/50',
    'Established': 'bg-purple-500/20 text-purple-400 border-purple-500/50',
    'Research': 'bg-purple-500/20 text-purple-400 border-purple-500/50',
    'Commercial': 'bg-purple-500/20 text-purple-400 border-purple-500/50',
    'Development': 'bg-purple-500/20 text-purple-400 border-purple-500/50',
    'Speculative': 'bg-gray-500/20 text-gray-400 border-gray-500/50'
  };
  
  // Get plant part and industry names
  const plantPart = plantParts?.find(p => p.id === product.plantPartId);
  const industry = industries?.find(i => i.id === product.industrySubCategoryId);
  
  // Get category-specific fallback image
  const getFallbackImage = () => {
    const fallbacks: Record<string, string> = {
      'Hemp Seed': '/images/fallbacks/hemp-seeds.jpg',
      'Hemp Bast (Fiber)': '/images/fallbacks/hemp-fiber.jpg',
      'Hemp Flowers': '/images/fallbacks/hemp-flower.jpg',
      'Hemp Roots': '/images/fallbacks/hemp-root.jpg',
      'Hemp Hurd (Shivs)': '/images/fallbacks/hemp-hurd.jpg',
      'Hemp Leaves': '/images/fallbacks/hemp-leaves.jpg'
    };
    return fallbacks[plantPart?.name || ''] || '/images/unknown-hemp-image.png';
  };
  
  // Support both naming conventions from API
  const productImageUrl = product.imageUrl || product.image_url;
  const imageUrl = imageError ? getFallbackImage() : (productImageUrl || getFallbackImage());
  const benefits = product.benefits_advantages || [];
  const isAIGenerated = productImageUrl?.includes('replicate') || productImageUrl?.includes('dall-e');
  
  // Mobile view - compact horizontal layout optimized for thumb navigation
  if (viewMode === 'mobile') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className="group bg-black/90 backdrop-blur-sm rounded-xl p-4 border border-gray-800 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 min-h-[88px] touch-manipulation">
          <div className="flex gap-4">
            {/* Thumbnail - Larger for better touch targets */}
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-900 flex-shrink-0">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-base line-clamp-2 mb-2 text-white leading-tight">{product.name}</h3>

              {/* Hemp-focused tags - Prominent display */}
              <div className="flex gap-2 mb-2 flex-wrap">
                {plantPart && (
                  <Badge className="text-xs bg-green-500/20 border-green-500/30 text-green-400 font-medium px-2 py-1">
                    <Leaf className="w-3 h-3 mr-1" />
                    {plantPart.name}
                  </Badge>
                )}
                {industry && (
                  <Badge className="text-xs bg-purple-500/20 border-purple-500/30 text-purple-400 font-medium px-2 py-1">
                    <Factory className="w-3 h-3 mr-1" />
                    {industry.name}
                  </Badge>
                )}
              </div>

              {/* Benefits preview - Single line */}
              {benefits.length > 0 && (
                <div className="text-xs text-gray-400 line-clamp-1">
                  {benefits[0].length > 40 ? `${benefits[0].substring(0, 40)}...` : benefits[0]}
                </div>
              )}
            </div>

            {/* Arrow - Larger touch target */}
            <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-purple-400 transition-all flex-shrink-0 self-center" />
          </div>
        </div>
      </Link>
    );
  }
  
  // List view
  if (viewMode === 'list') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className="group bg-black/90 backdrop-blur-sm rounded-xl p-4 cursor-pointer transition-all duration-300 hover:bg-gray-900/50 border border-gray-800 hover:border-purple-500/30 hover:shadow-purple-500/10">
          <div className="flex items-center gap-4">
            {/* Image - Larger for better visibility */}
            <div className="w-32 h-32 flex-shrink-0 rounded-lg overflow-hidden bg-gray-900 relative">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
              {isAIGenerated && (
                <Badge className="absolute top-2 right-2 bg-purple-500/30 backdrop-blur-sm text-purple-400 border-purple-500/50 text-xs">
                  <Sparkles className="w-3 h-3" />
                </Badge>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-4 mb-3">
                <h3 className="font-semibold text-lg text-white group-hover:text-purple-400 transition-colors leading-tight">
                  {product.name}
                </h3>
                <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} flex-shrink-0`}>
                  {stage}
                </Badge>
              </div>

              {/* Plant Part and Industry - Prominent Display */}
              <div className="flex flex-wrap gap-2 mb-3">
                {plantPart && (
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30 font-medium">
                    <Leaf className="w-3 h-3 mr-1" />
                    {plantPart.name}
                  </Badge>
                )}
                {industry && (
                  <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 font-medium">
                    <Factory className="w-3 h-3 mr-1" />
                    {industry.name}
                  </Badge>
                )}
              </div>
              
              {/* Benefits preview - Clean tertiary display */}
              {benefits.length > 0 && (
                <div className="mb-3">
                  <p className="text-xs text-gray-400 mb-1 font-medium">Benefits:</p>
                  <div className="flex flex-wrap gap-1">
                    {benefits.slice(0, 2).map((benefit, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs border-gray-600 text-gray-300 bg-gray-800/50">
                        {benefit.length > 25 ? `${benefit.substring(0, 25)}...` : benefit}
                      </Badge>
                    ))}
                    {benefits.length > 2 && (
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-400 bg-gray-800/50">
                        +{benefits.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
              
              {/* Description - Truncated with clean typography */}
              {product.description && (
                <p className="text-sm text-gray-300 line-clamp-2 mb-3 leading-relaxed">
                  {product.description}
                </p>
              )}

              {/* Company information if available */}
              {(product as any).hemp_company_products && (product as any).hemp_company_products.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-gray-400 mb-3">
                  <Building2 className="w-4 h-4 text-purple-400" />
                  <span>
                    {(product as any).hemp_company_products[0].hemp_companies.name}
                    {(product as any).hemp_company_products.length > 1 && ` +${(product as any).hemp_company_products.length - 1} more`}
                  </span>
                </div>
              )}

              {/* Action indicator */}
              <div className="flex items-center justify-end">
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-all group-hover:translate-x-1" />
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }
  
  // Grid view (default)
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-black/90 backdrop-blur-sm rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20 hover:-translate-y-1 border border-gray-800 hover:border-purple-500/30 hover:bg-gray-900/50">
        {/* Image - 16:9 aspect ratio for modern look */}
        <div className="aspect-[16/9] relative overflow-hidden bg-gray-900">
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={() => setImageError(true)}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

          {/* Stage badge */}
          <div className="absolute top-3 left-3">
            <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} backdrop-blur-sm border text-xs`}>
              {stage}
            </Badge>
          </div>
          
          {/* AI Generated badge */}
          {isAIGenerated && (
            <Badge className="absolute top-3 right-3 bg-purple-600/30 backdrop-blur-sm text-purple-400 border-purple-600/50 text-xs">
              <Sparkles className="w-3 h-3 mr-1" />
              AI
            </Badge>
          )}
        </div>
        
        {/* Content with Hemp-Focused Information */}
        <div className="p-5">
          {/* Product name - Bold, prominent */}
          <h3 className="font-bold text-lg line-clamp-2 text-white mb-3 leading-tight group-hover:text-purple-400 transition-colors">
            {product.name}
          </h3>

          {/* Plant Part and Industry - Most Prominent */}
          <div className="flex flex-wrap gap-2 mb-3">
            {plantPart && (
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-sm font-medium px-3 py-1">
                <Leaf className="w-3 h-3 mr-1" />
                {plantPart.name}
              </Badge>
            )}
            {industry && (
              <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 text-sm font-medium px-3 py-1">
                <Factory className="w-3 h-3 mr-1" />
                {industry.name}
              </Badge>
            )}
          </div>

          {/* Benefits - Clean tertiary display */}
          {benefits.length > 0 && (
            <div className="mb-4">
              <p className="text-xs text-gray-400 mb-2 font-medium">Key Benefits:</p>
              <div className="flex flex-wrap gap-1">
                {benefits.slice(0, 2).map((benefit, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs border-gray-600 text-gray-300 bg-gray-800/50">
                    {benefit.length > 20 ? `${benefit.substring(0, 20)}...` : benefit}
                  </Badge>
                ))}
                {benefits.length > 2 && (
                  <Badge variant="outline" className="text-xs border-gray-600 text-gray-400 bg-gray-800/50">
                    +{benefits.length - 2}
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Action indicator */}
          <div className="flex items-center justify-end">
            <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-all group-hover:translate-x-1" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default EnhancedProductCard;