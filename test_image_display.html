<!DOCTYPE html>
<html>
<head>
    <title>Test Image Display</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .product { margin: 20px 0; border: 1px solid #ccc; padding: 20px; }
        .product img { max-width: 200px; height: 150px; object-fit: cover; }
        .info { margin-left: 20px; display: inline-block; vertical-align: top; }
        .status { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Testing Image Display After Fix</h1>
    <div id="results"></div>

    <script>
        // Test if the API returns imageUrl correctly
        fetch('http://localhost:5173/api/products?limit=10')
            .then(res => res.json())
            .then(data => {
                const resultsDiv = document.getElementById('results');
                
                if (!data.products || data.products.length === 0) {
                    resultsDiv.innerHTML = '<p class="error">No products returned from API</p>';
                    return;
                }

                resultsDiv.innerHTML = '<h2>Products from API:</h2>';
                
                data.products.forEach((product, index) => {
                    const hasImageUrl = !!product.imageUrl;
                    const hasImage_url = !!product.image_url;
                    const imageUrl = product.imageUrl || product.image_url;
                    
                    const productDiv = document.createElement('div');
                    productDiv.className = 'product';
                    productDiv.innerHTML = `
                        <h3>${index + 1}. ${product.name}</h3>
                        <div>
                            <img src="${imageUrl || '/images/unknown-hemp-image.png'}" 
                                 alt="${product.name}"
                                 onerror="this.src='/images/unknown-hemp-image.png'; this.onerror=null;">
                            <div class="info">
                                <p><strong>API returns:</strong></p>
                                <p>imageUrl (camelCase): ${hasImageUrl ? '<span class="status">✓</span>' : '<span class="error">✗</span>'}</p>
                                <p>image_url (snake_case): ${hasImage_url ? '<span class="status">✓</span>' : '<span class="error">✗</span>'}</p>
                                <p><strong>Image URL:</strong> ${imageUrl ? imageUrl.substring(0, 80) + '...' : 'None'}</p>
                            </div>
                        </div>
                    `;
                    resultsDiv.appendChild(productDiv);
                });
            })
            .catch(err => {
                document.getElementById('results').innerHTML = `<p class="error">Error fetching products: ${err.message}</p>`;
            });
    </script>
</body>
</html>