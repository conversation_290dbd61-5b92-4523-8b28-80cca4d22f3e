#!/usr/bin/env python3
import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv('HempResourceHub/.env')

# Get database URL from environment
DATABASE_URL = os.getenv('DATABASE_URL')
if not DATABASE_URL:
    print("Error: DATABASE_URL not set")
    exit(1)

# Connect to database
conn = psycopg2.connect(DATABASE_URL)
cursor = conn.cursor()

# Query for Supabase storage URLs
query = """
SELECT image_url, name, description
FROM uses_products 
WHERE image_url LIKE '%supabase.co%'
ORDER BY 
    CASE 
        WHEN LOWER(image_url) LIKE '%unknown%' THEN 1
        WHEN LOWER(image_url) LIKE '%placeholder%' THEN 2
        WHEN LOWER(image_url) LIKE '%generic%' THEN 3
        WHEN LOWER(image_url) LIKE '%default%' THEN 4
        ELSE 5
    END,
    image_url
LIMIT 30;
"""

print("Checking for Supabase storage URLs in the database...")
print("=" * 80)

cursor.execute(query)
results = cursor.fetchall()

count = 0
for row in results:
    count += 1
    image_url, name, description = row
    print(f"\nProduct: {name}")
    print(f"URL: {image_url}")
    if description:
        print(f"Description: {description[:100]}...")

if count == 0:
    print("No Supabase storage URLs found in the database.")
else:
    print(f"\n{'-' * 80}")
    print(f"Total Supabase URLs found: {count}")

# Also check for any generic/placeholder keywords in product names or descriptions
print("\n" + "=" * 80)
print("Checking for generic/placeholder products...")

generic_query = """
SELECT image_url, name, description
FROM uses_products 
WHERE (
    LOWER(name) LIKE '%unknown%' OR
    LOWER(name) LIKE '%placeholder%' OR
    LOWER(name) LIKE '%generic%' OR
    LOWER(name) LIKE '%default%' OR
    LOWER(description) LIKE '%placeholder%' OR
    LOWER(description) LIKE '%generic hemp%'
)
AND image_url IS NOT NULL
LIMIT 10;
"""

cursor.execute(generic_query)
generic_results = cursor.fetchall()

count = 0
for row in generic_results:
    count += 1
    image_url, name, description = row
    print(f"\nGeneric Product: {name}")
    print(f"URL: {image_url}")

if count == 0:
    print("No generic/placeholder products found.")

# Close connection
cursor.close()
conn.close()