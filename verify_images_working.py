#!/usr/bin/env python3
"""Verify that images are working after the fix."""

import os
import json
import urllib.request
import urllib.error

# Read .env file
def load_env():
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"').strip("'")

load_env()

def test_api_response():
    """Test the API response to see if imageUrl is present."""
    print("Testing API response...")
    print("-" * 60)
    
    try:
        # Test the API endpoint
        url = "http://localhost:5173/api/products?limit=5"
        req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req) as response:
            data = json.loads(response.read().decode())
            
            if 'products' not in data:
                print("ERROR: No products field in API response")
                return
            
            print(f"Found {len(data['products'])} products in API response\n")
            
            # Check each product
            for i, product in enumerate(data['products'], 1):
                print(f"{i}. {product.get('name', 'Unknown')}")
                
                # Check which field is present
                has_camelCase = 'imageUrl' in product
                has_snake_case = 'image_url' in product
                
                print(f"   - imageUrl (camelCase): {'✓' if has_camelCase else '✗'}")
                print(f"   - image_url (snake_case): {'✓' if has_snake_case else '✗'}")
                
                # Get the actual URL
                image_url = product.get('imageUrl') or product.get('image_url')
                if image_url:
                    print(f"   - URL: {image_url[:80]}...")
                else:
                    print("   - URL: None")
                print()
            
            # Summary
            print("\nSUMMARY:")
            print("-" * 60)
            camelCase_count = sum(1 for p in data['products'] if 'imageUrl' in p)
            snake_case_count = sum(1 for p in data['products'] if 'image_url' in p)
            print(f"Products with imageUrl (camelCase): {camelCase_count}/{len(data['products'])}")
            print(f"Products with image_url (snake_case): {snake_case_count}/{len(data['products'])}")
            
    except urllib.error.URLError as e:
        print(f"ERROR: Could not connect to API: {e}")
        print("\nMake sure both servers are running:")
        print("1. Backend: npm run dev")
        print("2. Frontend: npx vite")
    except Exception as e:
        print(f"ERROR: {e}")

def check_image_files():
    """Check if image files exist locally."""
    print("\n\nChecking local image files...")
    print("-" * 60)
    
    image_dirs = [
        'HempResourceHub/client/public/images',
        'HempResourceHub/client/public/images/products',
        'HempResourceHub/client/public/generated_images'
    ]
    
    total_images = 0
    for dir_path in image_dirs:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if f.endswith(('.png', '.jpg', '.jpeg', '.webp'))]
            print(f"{dir_path}: {len(files)} images")
            total_images += len(files)
        else:
            print(f"{dir_path}: Directory not found")
    
    print(f"\nTotal local images found: {total_images}")

if __name__ == "__main__":
    print("VERIFYING IMAGE SYSTEM AFTER FIX")
    print("=" * 60)
    test_api_response()
    check_image_files()
    
    print("\n\nRECOMMENDATION:")
    print("-" * 60)
    print("1. Open http://localhost:5173 in your browser")
    print("2. Navigate to the Products page")
    print("3. Images should now be displaying correctly")
    print("\nIf images are still not showing:")
    print("- Check browser console for errors")
    print("- Try hard refresh (Ctrl+Shift+R or Cmd+Shift+R)")
    print("- Verify both servers are running")