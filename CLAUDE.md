# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## MCP Server Integration (Linux Environment)

Claude Code now has direct access to multiple MCP (Model Context Protocol) servers, providing native capabilities for database operations, web scraping, and more. These tools eliminate the need for many Python scripts.

### Available MCP Servers:
- **Supabase**: Direct database operations, migrations, edge functions (NOW WITH WRITE ACCESS!)
- **GitHub**: Repository management, issues, PRs, code search
- **Playwright**: Browser automation, web scraping, testing
- **Desktop Commander**: Enhanced file operations, code search
- **Notion**: Documentation and database management
- **Brave Search**: Real-time web and local search
- **Context7**: Library documentation access
- **IDE**: Code diagnostics and Jupyter execution

### Supabase MCP Write Access Configuration:
```bash
# Remove read-only configuration
claude mcp remove supabase

# Add with write access (service role key)
claude mcp add supabase "npx -y @supabase/mcp-server-supabase@latest --project-ref ktoqznqmlnxrtvubewyz" \
  -e SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Restart Claude Code for changes to take effect
```

## Current Status (January 18, 2025 - All 3 Agents Successfully Deployed!)

### 🚀 Database Expansion Progress
- **Current Products**: 2,562 (25.6% of 10,000 goal) 🎉
- **Session Growth**: +276 products today (11% increase)
- **Total Growth**: From 1,106 → 2,562 products (132% total increase)
- **MCP Write Access**: ✅ Fixed service role key (typo corrected)
- **All Agents Operational**: Patent Mining V2, Academic Research, Regional/Cultural
- **Duplicate Prevention**: V2 system working effectively (85% threshold)

### 📊 Database Statistics
- **Total Products**: 2,562 unique products
- **Today's Additions**:
  - Academic Research Agent: 97 products (from 72 papers)
  - Regional/Cultural Agent: 65 products (11 regions)
  - Background Patent Mining: ~114 products
- **Companies**: 204 total
- **Industries**: 42 subcategories across 16 main industries
- **Image Coverage**: ~90% (estimated)
- **Duplicates**: <1% with V2 prevention system
- **Source Diversity**: 3 active AI agents contributing

### ✅ Recent Improvements (January 18, 2025)
- **Service Role Key Fixed**: Corrected typo enabling database writes
- **API-Based Agents**: Created to bypass IPv6 issues in WSL2
- **Patent Mining V2**: Enhanced with fuzzy matching duplicate prevention
- **Database Doubled**: Added 1,180 new products in one session
- **Duplicate Analysis**: Prevented 773 potential duplicates
- **Automation Working**: All agents operational 24/7

### 🤖 Fixed Systems
1. **Academic Research Agent**: No more malformed names, quality scoring added
2. **Patent Mining Agent V2**: Dynamic descriptions, quality metrics
3. **Duplicate Detection**: Threshold adjusted to 0.95 (was 0.85)
4. **Plant Part Assignment**: Fixed incorrect Cannabinoid assignments
5. **Mega Agent Coordinator**: Running continuous automation
6. **Quality Validation**: All new products properly scored

### 🎯 Recent Fixes (January 10, 2025)
- **Fixed Name Parsing**: No more "Hemp The  Potential" errors
- **Added Quality Scoring**: Both agents now score products 0.5-1.0
- **Dynamic Descriptions**: Replaced repetitive templates
- **Database Cleanup**: Removed 212 problematic entries
- **Automation Restored**: 3 processes running continuously
- **Monitoring Active**: Logs available in logs/automation_*.log

## Quick Start Commands

```bash
# Development
cd HempResourceHub
npm install

# Start servers (need 2 terminals)
# Terminal 1: Backend
npm run dev                    # Starts Express server on :3001

# Terminal 2: Frontend  
npx vite                       # Starts Vite dev server on :5173

# Access the app at http://localhost:5173
```

## MCP-First Commands (Preferred)

```bash
# Database Operations (via Supabase MCP)
# Use Claude Code to:
# - Execute SQL queries directly
# - Apply migrations
# - Generate TypeScript types
# - Deploy edge functions
# - Check database advisors

# Web Scraping (via Playwright MCP)
# Use Claude Code to:
# - Navigate to hemp industry websites
# - Extract product/company data
# - Take screenshots for documentation
# - Automate form filling

# GitHub Operations (via GitHub MCP)
# Use Claude Code to:
# - Create issues/PRs
# - Search code across repos
# - Manage branches
# - Deploy changes

# File Operations (via Desktop Commander MCP)
# Use Claude Code to:
# - Search code with ripgrep
# - Edit multiple files
# - Manage processes

## Python Scripts (Legacy - Use MCP when possible)

# Multi-Provider AI & Automation
source venv_dedup/bin/activate
./RUN_AUTOMATION.sh                        # Start hourly automation
python monitor_automation.py               # Check automation status
python simple_agent_dashboard.py           # Agent performance dashboard
tail -f logs/automation_hourly_*.log       # View live automation logs

# Quality & Cleanup
python comprehensive_data_quality_analysis.py  # Full quality report
python verify_cleanup.py                       # Verify database quality

# Image Management
python generate_product_images.py             # Generate AI images
python check_image_status.py                  # Check image coverage

# Unified CLI
./hemp agent research "query" --features company image
./hemp monitor --live

# NEW: Database Expansion (761 → 50,000 Products) - ACTIVE!
python src/agents/specialized/patent_mining_agent_simple.py   # Patent discovery (WORKING!)
python src/agents/mega_agent_coordinator_v2.py --interactive  # Interactive coordinator
python src/agents/mega_agent_coordinator_v2.py --phase 1      # Run Phase 1 agents
python src/agents/mega_agent_coordinator_v2.py --report       # Progress report
python analyze_database_gaps.py                               # Find expansion opportunities
```

## Project Architecture

### Tech Stack
- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + Drizzle ORM
- **Database**: PostgreSQL via Supabase
- **UI**: shadcn/ui components + custom animations
- **State**: React Query (TanStack Query)

### Database Schema
```
hemp_plant_archetypes → plant_parts → uses_products
                                    ↘ industry_sub_categories
                                    ↘ hemp_companies
                                    ↘ research_entries
```

### Key Tables (Actual Names)
- `hemp_plant_archetypes` (NOT plant_types)
- `uses_products` (NOT hemp_products)
- `industry_sub_categories` (NOT sub_industries)
- `research_entries` (NOT research_papers)
- `plant_parts` with `plant_type_id` FK

## Recent Features & Fixes

### UI/UX Enhancements (Augment Code)
- **Navigation**: Simplified to single `/products` route
- **Admin Panel**: 9→5 tabs with dropdown selectors
- **Visual**: Modern cards, gradients, hemp growing loader
- **Performance**: 53% navigation complexity reduction
- **Animations**: 4 new components (hemp-growing-loader, etc.)

### Data & Backend (Claude)
- **Product Discovery**: Manual + Python scripts
- **Company System**: Extraction, relationships, deduplication
- **Image Generation**: Integrated with research agent
- **Attribution**: Legal compliance for scraped content

### UI/UX Major Update (January 11, 2025)
- **Unified Sidebar Navigation**: Consolidated navigation into a clean, consistent sidebar across all pages
  - Fixed positioning with smooth transitions
  - Mobile-responsive with automatic collapse
  - Marine-themed color palette (deep blues and teals)
  - Consistent iconography using Lucide React icons
  
- **Responsive Design with Device Detection**:
  - Device-aware layouts (desktop, tablet, mobile)
  - Touch-optimized interactions for mobile devices
  - Adaptive spacing and typography based on screen size
  - Smooth transitions between breakpoints
  
- **Marine-Themed Dashboard Components**:
  - Ocean-inspired color gradients
  - Wave-like animations and transitions
  - Deep blue primary colors with teal accents
  - Consistent visual language across all components
  
- **Desktop-First Homepage Design**:
  - Full-screen hero sections optimized for large displays
  - Multi-column layouts for efficient information density
  - Enhanced typography hierarchy for better readability
  - Strategic use of whitespace for visual breathing room
  
- **Spacing and Typography Improvements**:
  - Consistent spacing system (8px base unit)
  - Improved heading hierarchy (h1-h6)
  - Better line-height ratios for readability
  - Responsive font sizing using clamp()
  - Enhanced contrast ratios for accessibility

### Fixed Issues
1. ✅ Database table name mismatches
2. ✅ Image display with proper fallbacks
3. ✅ Duplicate image generation loop
4. ✅ Research frontend column mapping
5. ✅ SSL/authentication for development
6. ✅ Content Security Policy for fonts
7. ✅ All npm vulnerabilities (prismjs, esbuild)
8. ✅ GitHub Actions updated to latest versions
9. ✅ SSL certificate issues for Drizzle database connections
10. ✅ Claude API credit errors (service temporarily disabled)
11. ✅ TypeScript component errors (25+ fixed)
12. ✅ Supabase client API usage (.table → .from)
13. ✅ Property name mappings (industryId, benefits_advantages, etc.)
14. ✅ Missing module implementations
15. ✅ Deduplication system (found 66 duplicates)
16. ✅ Plant-part agent architecture designed
17. ✅ Database migration for versioning ready

## Environment Variables

```bash
# Required
VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co
VITE_SUPABASE_ANON_KEY=[from Supabase dashboard]
DATABASE_URL=postgresql://postgres:[password]@db.ktoqznqmlnxrtvubewyz.supabase.co:5432/postgres

# Optional
SUPABASE_SERVICE_ROLE_KEY=[for admin operations]
OPENAI_API_KEY=[for AI features]
```

## Development Workflow

1. **Frontend Changes**: Edit components in `/client/src/`, hot-reload via Vite
2. **Database Changes**: Update `/shared/schema.ts`, run `npm run db:push`
3. **API Changes**: Update `/server/routes.ts` and `/server/storage-db.ts`
4. **Python Scripts**: Use for data population and scraping
5. **Testing**: `npm run test` for API tests

## Key Components & Files

### Frontend
- `/client/src/components/ui/` - Reusable UI components
- `/client/src/components/animations/` - Hemp-themed animations
- `/client/src/pages/` - Route page components
- `/client/src/hooks/` - React Query data hooks

### Backend
- `/server/index.ts` - Express server setup
- `/server/routes.ts` - API endpoints
- `/server/storage-db.ts` - Database queries
- `/shared/schema.ts` - Shared type definitions

### Python Scripts
- `enhanced_research_scraper.py` - PubMed/article scraping
- `enhanced_company_scraper.py` - Logo extraction
- `run_simple_scrapers.py` - Pipeline runner
- `quick_add_product.py` - Manual product entry

### UI Components Created
- `bento-grid.tsx` - Classic bento grid homepage layout
- `bento-grid-v2.tsx` - Enhanced bento with animations
- `sidebar.tsx` - Unified navigation sidebar (NEW!)
- `device-wrapper.tsx` - Responsive device detection wrapper (NEW!)
- `marine-dashboard.tsx` - Ocean-themed dashboard layout (NEW!)
- `smart-product-image.tsx` - Intelligent image with fallbacks
- `enhanced-product-card.tsx` - Responsive cards with benefits
- `attributed-image.tsx` - Image with source attribution
- `hemp-growing-loader.tsx` - Plant growth animation
- `smart-search.tsx` - AI-powered search
- `interactive-product-card.tsx` - Enhanced cards
- `data-visualization-dashboard.tsx` - Analytics
- `alphabet-filter.tsx` - A-Z filtering component

## Common Tasks

### Adding Products (MCP-First Approach)

#### Via Supabase MCP (Preferred)
```sql
-- Use Claude Code to execute SQL directly:
INSERT INTO uses_products (name, description, industry_id, ...) 
VALUES ('Hemp Product Name', 'Description', 1, ...);
```

#### Via Playwright MCP (Web Scraping)
```
-- Ask Claude Code to:
1. Navigate to hemp industry websites
2. Extract product information
3. Insert directly into database
```

#### Legacy Python Scripts
```python
# Manual entry
python quick_add_product.py

# Bulk import
python bulk_import_products.py
```

### Web Scraping (MCP-First Approach)

#### Via Playwright MCP (Preferred)
```
-- Ask Claude Code to:
1. Use Playwright to visit target websites
2. Extract data using CSS selectors
3. Store results directly in Supabase
```

#### Legacy Python Scripts
```python
# If MCP approach doesn't work:
python enhanced_company_scraper.py
python hemp_industry_daily_scraper.py
```

### Troubleshooting

#### MCP Server Issues
- **Supabase MCP**: Ensure SUPABASE_ACCESS_TOKEN is set in Claude Code
- **Playwright MCP**: Browser will auto-install if needed
- **Desktop Commander**: Check file permissions for Linux
- **General**: MCP servers are configured in Claude Code CLI, not project files

#### Puppeteer in WSL (for Python scripts)
```bash
sudo apt-get update
sudo apt-get install -y chromium-browser
sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0
```

#### Database Connection
- Use Supabase MCP for direct database operations
- For Python scripts: Check URL encoding for special characters
- Use `sslmode=disable` for local development
- Ensure all environment variables are set

#### Image Issues
- Verify `/client/public/images/` directory exists
- Check for proper `?url` suffix in Vite imports
- Use fallback images for missing content

#### Express Server API Flooding
If you see rapid POST requests to `/api/ai/conversations`:
- This happens when AI services are unavailable but frontend keeps retrying
- Fix: AI routes now return 503 Service Unavailable with proper error messages
- Frontend hooks stop retrying on 503 errors
- AI components temporarily disabled in admin and product pages

## Important Notes

- Always use `plant_type_id` not `archetype_id` (DB uses the former)
- Products table is `uses_products` not `hemp_products`
- Research table is `research_entries` not `research_papers`
- Use SERVICE_ROLE_KEY for admin operations (bypasses RLS)
- Keep git commits under 50MB (no node_modules)

### AI Service Status (July 1, 2025)
- **Claude API**: Temporarily disabled due to insufficient credits
- **To Re-enable**:
  1. Add credits to Anthropic account
  2. Uncomment line 12 in `/server/routes.ts`
  3. Uncomment line 400 in `/server/routes.ts`
- **SSL Fix for Drizzle**: Use `NODE_TLS_REJECT_UNAUTHORIZED=0 npm run db:push`

### Known Issues (July 3, 2025)
- **TypeScript Errors**: ~16 remaining errors (mostly server-side) - down from 229
- **Missing npm lint script**: Add ESLint configuration
- **SSL Certificate Warning**: Requires NODE_TLS_REJECT_UNAUTHORIZED=0 for DB operations
- **Fixed Issues**: 
  - ✅ All component TypeScript errors resolved
  - ✅ Express API flooding issue (was 100+ requests/second)
  - ✅ AI service unavailable errors now handled gracefully

## 🎯 Recommended Next Steps

### Phase 1: Data Quality Enhancement (Immediate)
1. **Complete Missing Data**
   - Assign commercialization stages to 854 products
   - Link 438 products to companies
   - Generate AI images for 409 products

2. **Description Improvement**
   - Rewrite 141 remaining template descriptions
   - Expand descriptions to 350+ characters minimum

3. **Final Cleanup**
   - Review and consolidate 21 similar products
   - Implement duplicate prevention

### Phase 2: UI/UX Enhancements (Weeks 1-2)
1. **Enhanced Search**
   - Add plant part and industry filters
   - Implement saved searches
   - Add quick preview modal

2. **Analytics Improvements**
   - Interactive charts with drill-down
   - Export functionality
   - Custom date ranges

### Phase 3: Growth Features (Month 1)
1. **API Development**
   - RESTful API with authentication
   - Developer documentation
   - Usage analytics

2. **User Accounts**
   - Registration/login system
   - Favorites and watchlists
   - Email notifications

### Short-term Goals (Month 1)
1. **Launch API Service** - Monetization opportunity
2. **Implement B2B Features** - Supplier directory, RFQ system
3. **Mobile App Development** - iOS/Android hemp database app

### Revenue Opportunities
- API Access: $99-999/month
- Premium Data: Enhanced details
- Lead Generation: Connect buyers/sellers
- Projected: $10K MRR within 6 months

See `HEMP_DATABASE_ANALYSIS_AND_STRATEGY_2025.md` for comprehensive analysis and detailed roadmap.

## Support & Feedback

- **Help**: Use `/help` command
- **Issues**: Report at https://github.com/anthropics/claude-code/issues
- **Docs**: https://docs.anthropic.com/en/docs/claude-code