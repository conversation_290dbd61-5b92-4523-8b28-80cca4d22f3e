#!/usr/bin/env python3
"""
Upload the unknown hemp image to Supabase Storage
"""
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Supabase client
url = os.getenv('SUPABASE_URL') or os.getenv('VITE_SUPABASE_URL')
key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY') or os.getenv('VITE_SUPABASE_ANON_KEY')

if not url or not key:
    print("ERROR: Supabase credentials not found in environment")
    exit(1)

supabase: Client = create_client(url, key)

# Path to the unknown hemp image
image_path = '/home/<USER>/projects/HQz-Ai-DB-MCP-3/HempResourceHub/client/public/images/unknown-hemp-image.png'

if not os.path.exists(image_path):
    print(f"ERROR: Image not found at {image_path}")
    exit(1)

print(f"Uploading unknown hemp image from: {image_path}")

# Read the image file
with open(image_path, 'rb') as f:
    image_data = f.read()

# Upload to Supabase Storage
bucket_name = 'product-images'
file_name = 'fallbacks/unknown-hemp-image.png'

try:
    # First, check if bucket exists
    buckets = supabase.storage.list_buckets()
    bucket_exists = any(b.name == bucket_name for b in buckets)
    
    if not bucket_exists:
        print(f"Creating bucket: {bucket_name}")
        supabase.storage.create_bucket(bucket_name, options={'public': True})
    
    # Upload the file
    print(f"Uploading to: {bucket_name}/{file_name}")
    response = supabase.storage.from_(bucket_name).upload(
        path=file_name,
        file=image_data,
        file_options={"content-type": "image/png", "upsert": "true"}
    )
    
    # Get the public URL
    public_url = supabase.storage.from_(bucket_name).get_public_url(file_name)
    
    print(f"\n✅ Successfully uploaded unknown hemp image!")
    print(f"Public URL: {public_url}")
    
    # Now update all fallback images to use this URL
    import psycopg2
    
    DATABASE_URL = os.getenv('DATABASE_URL')
    conn = psycopg2.connect(DATABASE_URL)
    cursor = conn.cursor()
    
    # Count products to update
    cursor.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE image_url IN (
            '/images/unknown-hemp-image.png',
            '/images/fiber-hemp.png',
            '/images/grain-hemp.png',
            '/images/cannabinoid-hemp.png',
            '/images/dual-use-hemp.png'
        )
    """)
    count = cursor.fetchone()[0]
    
    if count > 0:
        print(f"\nUpdating {count} products to use the Supabase fallback image...")
        
        cursor.execute("""
            UPDATE uses_products 
            SET image_url = %s 
            WHERE image_url IN (
                '/images/unknown-hemp-image.png',
                '/images/fiber-hemp.png',
                '/images/grain-hemp.png',
                '/images/cannabinoid-hemp.png',
                '/images/dual-use-hemp.png'
            )
        """, (public_url,))
        
        conn.commit()
        print(f"✅ Updated {cursor.rowcount} products")
    
    # Show final distribution
    cursor.execute("""
        SELECT 
            CASE 
                WHEN image_url = %s THEN 'Supabase Unknown Hemp Image (Fallback)'
                WHEN image_url LIKE 'https://ktoqznqmlnxrtvubewyz.supabase.co%' THEN 'Other Supabase Images'
                WHEN image_url LIKE '/generated_images/%' THEN 'Local Generated Images'
                WHEN image_url LIKE 'http%' THEN 'External URLs'
                ELSE 'Other'
            END as image_type,
            COUNT(*) as count
        FROM uses_products
        GROUP BY 1
        ORDER BY count DESC
    """, (public_url,))
    
    print("\n📊 Final Image URL Distribution:")
    results = cursor.fetchall()
    for row in results:
        if row and len(row) >= 2:
            print(f"  {row[0]}: {row[1]} products")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"ERROR uploading image: {str(e)}")
    exit(1)